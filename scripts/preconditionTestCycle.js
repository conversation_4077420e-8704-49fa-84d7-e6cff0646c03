import fetch from "node-fetch";
import fs from "fs";
import {dataBaker} from "./components/aws/lambda/data-baker.js";
import {awsSQS} from "./components/aws/sqs/sqs.js";
import {apiCall} from "./components/api/api-call.js";
import {apiCallV2} from "./components/api/api-call-v2.js";
const outputFile = "./generated_data/zephyrscale_data.json"
const zs_token = process.env.ZEPHYR_SCALE_TOKEN
const zephyr_scale_url = process.env.ZEPHYR_SCALE_URL

const pricParm = process.argv.slice(2)
let cycleKeyOrId = pricParm[0].split('=')[1]
let testCycleKey = "";

async function executionData() {

    let url = `${zephyr_scale_url}/testcycles/${cycleKeyOrId}`
    const response = await fetch(url, {
        method: "GET",
        headers: {
            "Content-Type": "application/json",
            'Authorization': zs_token,
        },
    });

    const res = await response.json();
    let testCycleId = res.id;

    testCycleKey = res.key;
    const objAllSteps = new Object();

    try {
        const response = await fetch(`${zephyr_scale_url}/testexecutions?testCycle=${testCycleKey}`, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
                'Authorization': zs_token,
            },
        });

        const res = await response.json();
        let value = res.values;

        var obj = new Object();

        for (let item in value){
            if (value[item].automated == false){
                let link = value[item].testCase.self;
                let new_link = link.split("versions")[0]+ "testscript";
                let tc_id = value[item].testCase.id
                let tc_key = link.split("testcases/")[1].split("/versions")[0]
                let project_key = tc_key.split("-")[0];

                obj[`${parseInt(item)+1}`] = {
                    'tcStepInfo': new_link,
                    'tcId': tc_id,
                    'tcKey': tc_key,
                    'projectKey': project_key
                };
            }
        }

    } catch (error) {
        console.error("Error:", error);
    }

    for (let key in obj){
        let testlink = obj[key].tcStepInfo

        try {
            const response = await fetch(testlink, {
                method: "GET",
                headers: {
                    "Content-Type": "application/json",
                    'Authorization': zs_token,
                },
            });

            const result = await response.json();
            let steps = result.text;

            let objTC = new Object();

            if ( steps.includes('Given the API url') ){
                objTC = apiCall(steps);
            }

            if ( steps.includes('Given API[v2] request') ){
                objTC = apiCallV2(steps);
            }

            if ( steps.includes('Given the SQS url') ){
                objTC = awsSQS(steps);
            }

            if ( steps.includes('lambda function') ) {
                objTC = dataBaker(steps);
            }

            objAllSteps[`Test_Sequence_Number_${key}`] = {
                TestCycleKey: testCycleKey,
                ProjectKey: obj[key].projectKey,
                TC_name: objTC.TC_name,
                TC_id: obj[key].tcId,
                TC_key: obj[key].tcKey,
                Method: objTC.Method,
                Url: objTC.Url,
                Header: objTC.Header,
                Body: objTC.Body,
                StatusCode: objTC.StatusCode,
                SafaValue: objTC.SafaValue,
                AwaitPar: objTC.AwaitPar,
                TimeOutPar: objTC.TimeOutPar,
                SafeParams: objTC.SafeParams,
                TestData: objTC.TestData
            };

        } catch (error) {
            console.error("Error:", error);
        }
    }

    var jsonString = JSON.stringify(objAllSteps, null, 2);

    fs.writeFile(outputFile, jsonString, (err) => {
        if (err) {
            console.log('Error writing file', err);
        } else {
            console.log('Successfully wrote file');
        }
    });
}

executionData()