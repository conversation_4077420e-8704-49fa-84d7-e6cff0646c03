import fs from 'fs';
import parquet from 'parquetjs';
import readline from 'readline';
import { S3Client, PutObjectCommand } from "@aws-sdk/client-s3";

const s3 = new S3Client({
    region: process.env.AWS_REGION,
    credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
        sessionToken: process.env.AWS_SESSION_TOKEN
    }
});

const LOG_FILE = './generated_data/k6_execution.log';
const BATCH_INTERVAL = process.env.LOGS_S3_BATCH_INTERVAL ?? 10000;
const INACTIVITY_LIMIT = process.env.LOG_WATCHER_INACTIVITY_LIMIT ?? 10000;
const AWS_BUCKET_NAME = process.env.LOGS_BUCKET_NAME_WITHOUT_ENV + "-" + process.env.ENVIRONMENT;

console.log("AWS bucket: " + AWS_BUCKET_NAME);

let lastActivityTime = Date.now();

async function processLogs() {
    if (!fs.existsSync(LOG_FILE)) {
        console.log('Log file does not exist. Waiting for logs...');
        checkForExit();
        return;
    }

    const logs = await readLogsFromFile(LOG_FILE);
    fs.writeFileSync(LOG_FILE, '', 'utf8'); // Clear log file immediately

    if (logs.length === 0) {
        console.log('No logs to process.');
        checkForExit();
        return;
    }

    lastActivityTime = Date.now(); // Reset inactivity timer

    const parquetFile = `requests_logs_${lastActivityTime}.parquet`;
    await convertJsonToParquet(logs, parquetFile);

    await uploadToS3(parquetFile, parquetFile);
}

async function readLogsFromFile(filename) {
    const logs = [];
    const fileStream = fs.createReadStream(filename);
    const rl = readline.createInterface({ input: fileStream, crlfDelay: Infinity });

    for await (const line of rl) {
        try {
            const log = JSON.parse(line.trim());
            logs.push(log);
        } catch (err) {
            console.warn('Skipping invalid JSON line:', line);
        }
    }

    return logs;
}

async function convertJsonToParquet(logs, parquetFile) {
    const schema = new parquet.ParquetSchema({
        ProjectKey: { type: 'UTF8', optional: true },
        ScenarioKey: { type: 'UTF8', optional: true },
        TestCase: { type: 'UTF8', optional: true },
        level: { type: 'UTF8' },
        ResponseCode: { type: 'INT32', optional: true },
        URL: { type: 'UTF8' },
        RequestHeader: { type: 'UTF8', optional: true },
        RequestBody: { type: 'UTF8', optional: true },
        ResponseHeader: { type: 'UTF8', optional: true },
        ResponseBody: { type: 'UTF8', optional: true },
        timestamp: { type: 'INT64', logicalType: 'TIMESTAMP_MILLIS' }
    });

    let writer = await parquet.ParquetWriter.openFile(schema, parquetFile, { compression: 'SNAPPY' });

    for (let log of logs) {
        await writer.appendRow(log);
    }
    await writer.close();
    console.log(`Converted logs to Parquet (Snappy compressed): ${parquetFile}`);
}

async function uploadToS3(filePath, s3Key) {
    const fileContent = fs.readFileSync(filePath);
    const uploadParams = {
        Bucket: AWS_BUCKET_NAME,
        Key: s3Key,
        Body: fileContent,
        ContentType: 'application/octet-stream', // Parquet file type
    };

    try {
        const command = new PutObjectCommand(uploadParams);
        const data = await s3.send(command);

        console.log(`File uploaded successfully. ETag: ${data.ETag}`);
        fs.unlinkSync(filePath);
    } catch (error) {
        console.error(`S3 Upload Error: ${error.message}`);
        fs.unlinkSync(filePath);
    }
}

// Check logs every 10 seconds
const interval = setInterval(processLogs, BATCH_INTERVAL);

// Stop script if no new logs for 10 sec
function checkForExit() {
    if (Date.now() - lastActivityTime > INACTIVITY_LIMIT) {
        console.log('No new logs detected for 10 seconds. Exiting...');
        clearInterval(interval);
        process.exit(0);
    }
}
