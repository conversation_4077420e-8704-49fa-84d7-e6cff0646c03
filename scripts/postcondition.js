import fetch from "node-fetch";
import fs from 'fs';
import path from "path";
import { createRequire } from 'module';

const require = createRequire(import.meta.url);
const TC_INFO = require('../generated_data/zephyrscale_data.json');
const FILE_PATH = path.resolve('./generated_data/summary.json');

// Validate required environment variables
const REQUIRED_ENV_VARS = [
    'AWS_REGION', 'ZEPHYR_SCALE_TOKEN', 'ZEPHYR_SCALE_URL',
    'DATA_DOG_URL', 'DATADOG_API_KEY', 'ENVIRONMENT'
];
for (const ENV_VAR of REQUIRED_ENV_VARS) {
    if (!process.env[ENV_VAR]) {
        throw new Error(`Missing required environment variable: ${ENV_VAR}`);
    }
}

const {
    AWS_REGION, ZEPHYR_SCALE_TOKEN, ZEPHYR_SCALE_URL,
    DATA_DOG_URL, DATADOG_API_KEY, ENVIRONMENT
} = process.env;
const PROJECT_KEY = TC_INFO.Test_Sequence_Number_1.ProjectKey;
const THREADS_USERS = parseInt(process.env.THREADS_USERS) || 1;

let interval;

// Utility function to split text into chunks
function splitMessage(text, maxLength) {
    const chunks = [];
    while (text.length > 0) {
        let chunk = text.slice(0, maxLength);
        const lastNewline = chunk.lastIndexOf('\n');
        if (lastNewline > 0) {
            chunk = chunk.slice(0, lastNewline + 1);
        }
        chunks.push(chunk);
        text = text.slice(chunk.length);
    }
    return chunks;
}

// Function to send data to Zephyr Scale
async function sendToZephyrScale(data) {
    try {
        const response = await fetch(`${ZEPHYR_SCALE_URL}/testexecutions`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                'Authorization': ZEPHYR_SCALE_TOKEN,
            },
            body: JSON.stringify(data),
        });
        console.log(`Zephyr Scale Response: ${response.status}`, await response.text());
    } catch (error) {
        console.error("Error sending to Zephyr Scale:", error);
    }
}

// Function to send metrics to DataDog
async function sendMetricsToDataDog(metricData) {
    try {
        const response = await fetch(`${DATA_DOG_URL}/series`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                'DD-API-KEY': DATADOG_API_KEY,
            },
            body: JSON.stringify(metricData),
        });
        console.log(`DataDog Response: ${response.status}`, await response.text());
    } catch (error) {
        console.error("Error sending to DataDog:", error);
    }
}

// Function to send notification to Slack
async function sendNotificationToSlack(webHookUrl, message) {
    try {
        const response = await fetch(webHookUrl, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify(message),
        });
        console.log(`Slack Response: ${response.status}`, await response.text());
    } catch (error) {
        console.error("Error sending to Slack:", error);
    }
}

// Main function to process results
function processResults(results, TC_INFO) {
    const resultsObj = {};
    for (let i in results) {
        const statusN = results[i].checks.some(check => check.fails > 0) ? 'Fail' : 'Pass';
        resultsObj[`Test_Sequence_Number_${parseInt(i) + 1}`] = statusN;
    }

    const allResults = Object.keys(resultsObj).map(item => {
        const { TC_id, ProjectKey, TC_key, TestCycleKey, TC_name } = TC_INFO[item];
        return {
            id: TC_id,
            projectKey: ProjectKey,
            testCaseKey: TC_key,
            testCycleKey: TestCycleKey,
            statusName: resultsObj[item],
            TC_name
        };
    });

    return allResults;
}

// Function to generate Slack message
function generateSlackMessage(allResults, environment, projectKey, testCycleKey, dataDogLink, awsRegion) {
    let pass = 0, fail = 0;
    let text = `
*Environment:* ${environment}
*ProjectKey:* ${projectKey}
*Zephyr Scale TestCycle:* <${projectKey !== 'COURSER' ?
        `https://justeattakeaway.atlassian.net/projects/${projectKey}?selectedItem=com.atlassian.plugins.atlassian-connect-plugin:com.kanoah.test-manager__main-project-page#!/testPlayer/${testCycleKey}` :
        `https://justeattakeaway.atlassian.net/projects/COURLIVE?selectedItem=com.atlassian.plugins.atlassian-connect-plugin:com.kanoah.test-manager__main-project-page#!/testPlayer/${testCycleKey}`} | ${testCycleKey}> 
*DataDog: *<${dataDogLink} | *metrics*>
*AWS Atena: *<https://${awsRegion}.console.aws.amazon.com/athena/home?region=${awsRegion}#/query-editor | *logs*>\n
*Total:* TOTAL | *Passed:* PASS | *Failed:* FAIL \n
`;

    allResults.forEach(result => {
        if (result.statusName === 'Pass') pass++; else fail++;
        text += `${result.statusName === 'Pass' ? ':white_check_mark:' : ':x:'}  <${projectKey !== 'COURSER' ?
            `https://justeattakeaway.atlassian.net/projects/${projectKey}?selectedItem=com.atlassian.plugins.atlassian-connect-plugin:com.kanoah.test-manager__main-project-page#!/testCase/${result.testCaseKey}` :
            `https://justeattakeaway.atlassian.net/projects/COURLIVE?selectedItem=com.atlassian.plugins.atlassian-connect-plugin:com.kanoah.test-manager__main-project-page#!/testCase/${result.testCaseKey}`} | ${result.testCaseKey}> [${result.TC_name}]\n`;
    });

    return text.replace("TOTAL", `${pass + fail}`).replace("PASS", `${pass}`).replace("FAIL", `${fail}`);
}

// Main function to check file and process data
function checkFile() {
    if (!fs.existsSync(FILE_PATH)) {
        console.log(`File ${FILE_PATH} not found.`);
        return;
    }

    console.log(`File ${FILE_PATH} was found.`);
    const res = require(FILE_PATH);
    const results = res.root_group.groups;

    const allResults = processResults(results, TC_INFO);

    // Send results to Zephyr Scale
    allResults.forEach(result => {
        const { TC_name, ...data } = result;
        sendToZephyrScale(data);
    });

    // Prepare and send metrics to DataDog
    const iterations = res.metrics.iterations.values.count;
    const vUser = res.metrics.vus?.values?.max || THREADS_USERS;
    const timeNow = Math.floor(Date.now() / 1000);

    const metricData = {
        series: [
            {
                metric: "ots.zs_info.iterations",
                type: 0,
                points: [{ timestamp: timeNow, value: iterations }],
                resources: [{ name: PROJECT_KEY, type: "project" }]
            },
            {
                metric: "ots.zs_info.vUser",
                type: 0,
                points: [{ timestamp: timeNow, value: vUser }],
                resources: [{ name: PROJECT_KEY, type: "project" }]
            }
        ]
    };
    sendMetricsToDataDog(metricData);

    // Generate Slack message
    const dataDogLink = `https://app.datadoghq.eu/dashboard/q93-6v3-6av/k6-core?tpl_var_environment%5B0%5D=${ENVIRONMENT}&` +
        (allResults[0].testCycleKey ? `tpl_var_test_cycle_key%5B0%5D=${allResults[0].testCycleKey}` : `tpl_var_project_key%5B0%5D=${allResults[0].projectKey}`);

    const slackMessage = generateSlackMessage(allResults, ENVIRONMENT, allResults[0].projectKey, allResults[0].testCycleKey, dataDogLink, AWS_REGION);
    const messageChunks = splitMessage(slackMessage, 3000);
    const blocks = messageChunks.map(chunk => ({
        type: "section",
        text: { type: "mrkdwn", text: chunk }
    }));

    const WEB_HOOK_URL_BY_PROJECT = {
        "CSI": process.env.SLACK_WEBHOOK_UNIVERSAL_OTS,
        "LPI": process.env.SLACK_WEBHOOK_COURQE_OTS,
        "SAPT": process.env.SLACK_WEBHOOK_SAPT_OTS,
        "CFIN": process.env.SLACK_WEBHOOK_CFIN_OTS,
        "COURBOAR": process.env.SLACK_WEBHOOK_COURBOAR_OTS,
        "DELIVERY": process.env.SLACK_WEBHOOK_DELIVERY_OTS,
        "COURSER": process.env.SLACK_WEBHOOK_COURSER_OTS,
        "COURQE": process.env.SLACK_WEBHOOK_COURQE_OTS
    };
    const WEB_HOOK_URL = WEB_HOOK_URL_BY_PROJECT[allResults[0].projectKey];

    if (WEB_HOOK_URL) {
        sendNotificationToSlack(WEB_HOOK_URL, { blocks });
    } else {
        console.error(`No Slack webhook URL found for project: ${allResults[0].projectKey}`);
    }

    clearInterval(interval);
}

interval = setInterval(checkFile, 2000);