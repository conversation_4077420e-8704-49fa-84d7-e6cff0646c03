import fetch from "node-fetch";
import fs from "fs";
import {dataBaker} from "./components/aws/lambda/data-baker.js";
import {awsSQS} from "./components/aws/sqs/sqs.js";
import {apiCall} from "./components/api/api-call.js";
import {apiCallV2} from "./components/api/api-call-v2.js";
const outputFile = "./generated_data/zephyrscale_data.json"
import cycleKeyForProjects from './components/utils/technical_multi_step_test_cycle.json' with { type: 'json' };

const zs_token = process.env.ZEPHYR_SCALE_TOKEN
const zephyr_scale_url = process.env.ZEPHYR_SCALE_URL

const pricParm = process.argv.slice(2)
let testCaseKey = pricParm[0].split('=')[1]

async function executionData() {

    let projectKey = testCaseKey.split("-")[0]
    const objAllSteps = new Object();
    try {
        const tcinfo = await fetch(`${zephyr_scale_url}/testcases/${testCaseKey}`, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
                'Authorization': zs_token,
            },
        });

        const result = await tcinfo.json();
        let testCaseId = result.id

        const response = await fetch(`${zephyr_scale_url}/testcases/${testCaseKey}/testscript`, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
                'Authorization': zs_token,
            },
        });

        const res = await response.json();

        let value = res.text;

        let arrvalue = value.split("Given")
        arrvalue.shift()

        var obj = new Object();
        for (let item in arrvalue){

            obj[`${parseInt(item)+1}`] = {
                'tcStepInfo': arrvalue[item],
                'tcId': testCaseId,
                'tcKey': testCaseKey,
                'projectKey': projectKey
            };
        }

    } catch (error) {
        console.error("Error:", error);
    }
    for (let key in obj){
        try {
            let steps = obj[key].tcStepInfo;
            let objTC = new Object();
            if ( steps.includes('Given the API url') ){
                objTC = apiCall(steps);
            }
            if ( steps.includes('API[v2] request') ){
                objTC = apiCallV2(steps);
            }
            if ( steps.includes('Given the SQS url') ){
                objTC = awsSQS(steps);
            }
            if ( steps.includes('lambda function') ) {
                objTC = dataBaker(steps);
            }
            objAllSteps[`Test_Sequence_Number_${key}`] = {
                TestCycleKey: cycleKeyForProjects[projectKey],
                ProjectKey: obj[key].projectKey,
                TC_name: objTC.TC_name,
                TC_id: obj[key].tcId,
                TC_key: obj[key].tcKey,
                Method: objTC.Method,
                Url: objTC.Url,
                Header: objTC.Header,
                Body: objTC.Body,
                StatusCode: objTC.StatusCode,
                SafaValue: objTC.SafaValue,
                AwaitPar: objTC.AwaitPar,
                TimeOutPar: objTC.TimeOutPar,
                SafeParams: objTC.SafeParams,
                TestData: objTC.TestData
            };

        } catch (error) {
            console.error("Error:", error);
        }
    }

    var jsonString = JSON.stringify(objAllSteps, null, 2);

    fs.writeFile(outputFile, jsonString, (err) => {
        if (err) {
            console.log('Error writing file', err);
        } else {
            console.log('Successfully wrote file');
        }
    });
}

executionData()