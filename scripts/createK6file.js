import fs from 'fs';

import { createRequire } from 'module';
const require = createRequire(import.meta.url);
const steps = require("../generated_data/zephyrscale_data.json");
const outputFile = "./generated_data/k6_script.js"

let options = process.env.OPTIONS
let using_test_data = process.env.USING_TEST_DATA

let elements = ` 
const data = JSON.parse(open("../generated_data/zephyrscale_data.json"));
`;
let useVariable = '';
let code = '';
for (let item in steps) {
    let strNumberIter = item.split("Test_Sequence_Number_")[1]
    let sleepPar = '';
    if (steps[item].AwaitPar != null) {
        sleepPar = `sleep(${steps[item].AwaitPar})`
    }
    let safeP = ``;
    for (let i in steps[item].SafeParams) {
        if (steps[item].SafeParams[i] !== undefined) {
            safeP = safeP + `${steps[item].SafeParams[i]} \n        `
            let strVariable = `let ${(steps[item].SafeParams[i]).split("=")[0]}\n    `
            useVariable = useVariable + strVariable
        }
    }
    let checkV = ``;
    for (let i in steps[item].SafaValue) {
        if (steps[item].SafaValue[i] !== undefined) {
            checkV = checkV + `'check body value${parseInt(i)+1}': (r) => ${ (steps[item].SafaValue[i]).replace("resBody", 'r.json()')}, \n                `
        }
    }

    let stepRequest = `
    
    group(\"${steps[item].TC_name}\", function () {
        const req = http.${ ( steps[item].Method == 'DELETE' ) ? "del" : (steps[item].Method).toLowerCase() }(\`${steps[item].Url}\`, 
            ${ ( steps[item].Method == 'GET' ) ? '' : `JSON.stringify(${steps[item].Body}),`}
                {
            headers: ${steps[item].Header}, timeout: ${ steps[item].TimeOutPar == null ? 60000 : steps[item].TimeOutPar }
            }
        );
        const checkOutput = check(req, { 
                'status code MUST be ${steps[item].StatusCode}': (r) => r.status == ${steps[item].StatusCode},
                ${checkV}
            });
        try{
            resBody = JSON.parse(req.body);
        } catch (error) {
            // console.log('no body');
        }
        logRequestData(req, checkOutput, '${steps[item].TC_key}')
        ${sleepPar}
        ${safeP}
        
    });\n`

    if ((steps[item].Header).includes("x-www-form-urlencoded")) {
        stepRequest = `
    group(\"${steps[item].TC_name}\", function () {
        const req = http.${ ( steps[item].Method == 'DELETE' ) ? "del" : (steps[item].Method).toLowerCase() }(\`${steps[item].Url}\`, 
            ${steps[item].Body},
                {
            headers: ${steps[item].Header}
            }
        );
        const checkOutput = check(req, { 
                'status code MUST be ${steps[item].StatusCode}': (r) => r.status == ${steps[item].StatusCode},
                ${checkV}
            });
        try{
            resBody = JSON.parse(req.body);
        } catch (error) {
            // console.log('no body');
        }
        logRequestData(req, checkOutput, '${steps[item].TC_key}')   
        ${sleepPar}
        ${safeP}
        
    });\n`
    }

    if (steps[item].Method == 'SQS') {
        stepRequest = `
    const testQueue = \`${steps[item].Url}\`

   const mesString = ${steps[item].Body};
   const sqsReg = await sqs.sendMessage(testQueue, JSON.stringify(mesString));
   logSQSEventData(${steps[item]}, testQueue, sqsReg);
   ${safeP}\n
   group(\"${steps[item].TC_name}\", function () {
    const req = http.request('GET', \`https://www.google.com\`,);
    check(req, {'status code MUST be 200': (r) => r.status == 200});
    ${sleepPar}
    });\n`
    }


    if (steps[item].TestData != null) {
        let stepRequestS = stepRequest.split("function () {")
        if ( !JSON.parse(using_test_data) ){
            elements = elements + `
const data${strNumberIter}  = new SharedArray('users${strNumberIter}', function () {
    const f = JSON.parse(open("../generated_data/zephyrscale_data.json"));
    const d = f.${item}.TestData
    return d;
});\n`
            stepRequest = `
    ${stepRequestS[0]} function () {\n
    const testData = data${strNumberIter}[Math.floor(Math.random() * data${strNumberIter}.length)];
    ${stepRequestS[1]}
    `
            stepRequest = stepRequest.replace(/testData/g, `testData${strNumberIter}`)
        }else{
            elements = elements + `
let dataExam${strNumberIter} = data.${item}.TestData`
            stepRequest = `
    ${stepRequestS[0]} function () {\n
        const testData = dataExam${strNumberIter}[__VU - 1];
    ${stepRequestS[1]}
    `
        }
    }
    code = code + stepRequest;
}

let content = `
import http from 'k6/http';
import { check, sleep, group } from 'k6';
import { SharedArray } from 'k6/data';
import {logRequestData, logSQSEventData} from "../scripts/components/utils/k6_utils.js";

import { AWSConfig, SQSClient } from 'https://jslib.k6.io/aws/0.11.0/sqs.js'
const awsConfig = new AWSConfig({
   region: "us-east-1",
   accessKeyId: __ENV.AWS_ACCESS_KEY_ID,
   secretAccessKey: __ENV.AWS_SECRET_ACCESS_KEY,
   sessionToken: __ENV.AWS_SESSION_TOKEN,
})
const sqs = new SQSClient(awsConfig)

${elements}

export const options = ${options};

function randomNumberInSetRange(min, max) {
    min = Math.ceil(min);
    max = Math.floor(max);
    return Math.floor(Math.random() * (max - min) + min); 
}

function randomNumberOfSetLength(digitsNumber) {
    const min = Math.pow(10, digitsNumber - 1);
    const max = Math.pow(10, digitsNumber) - 1;
    return Math.floor(Math.random() * (max - min + 1)) + min;
}

function randomString(str) {
    var rnd = '';
    while (rnd.length < str) 
        rnd += Math.random().toString(36).substring(2);
    return rnd.substring(0, str);
};

function currentDataUTC(changeValue=0) {
    const dUTC = ( Date.now() - Date.now()%1000 ) / 1000 + changeValue
    return dUTC
};

function currentTimeStamp(changeValue=0) {
    const formDate = new Date();
    formDate.setSeconds(formDate.getSeconds() + changeValue );
    let changeformDate = formDate.toISOString()
    return changeformDate
};

function currentTime(changeValue=0) {
    const oTime = currentTimeStamp(changeValue).split("T")[1].split(".")[0];
    return oTime
};

function currentData(changeValue=0) {
    const oData = currentTimeStamp(changeValue*86400).split("T")[0];
    return oData
};

function currentDayOfWeek(changeValue=0) {
    const days = [
        'Sunday',
        'Monday',
        'Tuesday',
        'Wednesday',
        'Thursday',
        'Friday',
        'Saturday'
      ];
    const curData = new Date();
    const dayOfWeek = (70000 + curData.getDay() + changeValue) % 7;
    return days[dayOfWeek]
};

export default async function () {

    ${useVariable}
    let resBody;
    let RequestHeader;
    let RequestBody;
    let ResponseHeader;
    let ResponseBody;
    
    ${code}
}

export function handleSummary(data) {
    return {
      './generated_data/summary.json': JSON.stringify(data),
    };
  }
`;

fs.writeFile(outputFile, content, (err) => {
    if (err) throw err;
    console.log('The file has been saved');
});