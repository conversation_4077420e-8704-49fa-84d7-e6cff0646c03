import fetch from "node-fetch";

const zs_token = process.env.ZEPHYR_SCALE_TOKEN
const zephyr_scale_url = process.env.ZEPHYR_SCALE_URL
const data_dog_url = process.env.DATA_DOG_URL
const datadog_api_key = process.env.DATADOG_API_KEY

const pricParm = process.argv.slice(2)
const projKey = pricParm[0].split('=')[1].split("-")[0]

let automated = 0;
let manual = 0;

async function sendStatistics() {
    const totalTC = await fetch(`${zephyr_scale_url}/testcases?projectKey=${projKey}&startAt=0&maxResults=1000`, {
        method: "GET",
        headers: {
            "Authorization": zs_token
        },
    });
    const restotalTC = await totalTC.json();
    const tCasesTotal = restotalTC.total


    const testExecutions = await fetch(`${zephyr_scale_url}/testexecutions?projectKey=${projKey}&startAt=0&maxResults=10000&onlyLastExecutions=true`, {
        method: "GET",
        headers: {
            "Authorization": zs_token
        },
    });
    const resTestExecutions = await testExecutions.json();
    let arrEx = resTestExecutions.values
    let listAutomate = {}
    let listManual = {}

    for (let n in arrEx){
        if (arrEx[n].automated == true) {
            listAutomate[arrEx[n].testCase.self.split("testcases/")[1].split("/versions")[0]] = arrEx[n].actualEndDate
        }else {
            listManual[arrEx[n].testCase.self.split("testcases/")[1].split("/versions")[0]] = arrEx[n].actualEndDate
        }
    }

    for (let k in listAutomate){
        if ( Object.keys(listManual).includes(k) ){
            if ( listAutomate[k]>listManual[k] ) {
                delete listManual[k]
            }else{
                delete listAutomate[k]
            }
        }
    }

    console.log("∨∨∨∨∨∨∨∨∨∨∨∨∨∨∨∨∨∨")
    console.log(projKey + "_Total->" + tCasesTotal)
    console.log(" ")
    automated = Object.keys(listAutomate).length
    console.log(projKey + " Automate List length -> "+ automated)
    console.log(listAutomate)
    console.log(" ")
    manual = Object.keys(listManual).length
    console.log(projKey + " Manual List length -> "+ manual)
    console.log(listManual)
    console.log("∧∧∧∧∧∧∧∧∧∧∧∧∧∧∧∧∧∧")

    let timeNow = (Date.now() - Date.now() % 1000) / 1000
    let data = {
        "series": [
            {
                "metric": "ots.zs_info.total_test_cases",
                "type": 0,
                "points": [
                    {
                        "timestamp": timeNow,
                        "value": tCasesTotal
                    }
                ],
                "resources": [
                    {
                        "name": `${projKey}`,
                        "type": "project"
                    }
                ]
            },
            {
                "metric": "ots.zs_info.automate_test_cases",
                "type": 0,
                "points": [
                    {
                        "timestamp": timeNow,
                        "value": automated
                    }
                ],
                "resources": [
                    {
                        "name": `${projKey}`,
                        "type": "project"
                    }
                ]
            },
            {
                "metric": "ots.zs_info.manual_test_cases",
                "type": 0,
                "points": [
                    {
                        "timestamp": timeNow,
                        "value": manual
                    }
                ],
                "resources": [
                    {
                        "name": `${projKey}`,
                        "type": "project"
                    }
                ]
            }
        ]
    }

    const response = await fetch(`${data_dog_url}/series`, {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
            'DD-API-KEY': `${datadog_api_key}`,
        },
        body: JSON.stringify(data),
    });

    let resp = await response.json()
    console.log(resp);
}

sendStatistics();