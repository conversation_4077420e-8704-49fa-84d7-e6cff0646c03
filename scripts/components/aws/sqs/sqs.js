import {envUrl, checkValues, examplData, saveParams} from "../../utils/utils.js"
function awsSQS (steps){
    let objTC = new Object();

    let testScript = steps.replace(/ /g,'').replace(/\n/g, '')
    let tcName = steps.split('Scenario "')[1].split('"')[0]
    let url = envUrl(testScript);
    let method = 'SQS'
    let header = "{}";
    let body = "{}";
    if ( testScript.includes('body:"""') ) {
        body = testScript.split('body:"""')[1].split('"""')[0]
    }
    let strThen = testScript.split("Then")[1]
    let statusCode = strThen.split("code:")[1].split("And")[0].split("Examples:")[0]
    let checkValue = checkValues(strThen);
    let awitPar = null
    if ( testScript.includes('willwait') ) {
        awitPar = testScript.split("willwait")[1].split("seconds")[0]
    }
    let parObj = saveParams(steps);
    let resultData = null
    if ( steps.includes('Examples:') ) {
        resultData = examplData(steps)
    }

    return objTC = {
        TC_name: tcName,
        Method: method,
        Url: url,
        Header: JSON.parse(JSON.stringify(header)),
        Body: JSON.parse(JSON.stringify(body)),
        StatusCode: statusCode,
        SafaValue: checkValue,
        AwaitPar: awitPar,
        SafeParams: parObj,
        TestData: resultData
    };
}

export {awsSQS}