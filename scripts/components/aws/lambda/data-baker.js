import {checkValues, examplData, saveParams} from "../../utils/utils.js"

function dataBaker (steps){
    let objTC = new Object();
    const urls = {
        "dev": process.env.DATA_TEST_LIB_LAMBDA_DEV_URL,
        "staging": process.env.DATA_TEST_LIB_LAMBDA_STAGING_URL,
        "stageu1": process.env.DATA_TEST_LIB_LAMBDA_STAGEU1_URL,
        "staguk": process.env.DATA_TEST_LIB_LAMBDA_STAGUK_URL,
        "stagaus": process.env.DATA_TEST_LIB_LAMBDA_STAGAUS_URL
    }
    let testScript = steps.replace(/ /g,'').replace(/\n/g, '')
    let tcName = steps.split('scenario "')[1].split('"')[0]
    let urlAction = testScript.split('actionon')[1].split('environment')[0]
    // Check if urlAction is "<env>", then assign process.env.ENVIRONMENT
    if (urlAction === "<env>") {
        urlAction = process.env.ENVIRONMENT;
    }
    let url = urls[urlAction]
    let method = "POST"
    let header = "{'Content-Type':'application/json'}";
    let bodyaction = testScript.split('functionwitha"')[1].split('"actionon')[0]
    let bodyparameters = steps.replace(/\n/g, '').split('project')[1].split('When')[0].split('And')
    bodyparameters.shift()
    let bodyparam = '{';
    for (let i = 0; i < bodyparameters.length; i++) {
        let [key, value] = bodyparameters[i].split(':');
        key = key.replace(/ /g, '')
        if (value.includes('${')) {
            value = value.replace(/^"|"$/g, '');
            bodyparam += `  "${key}": ${value}`;
        }else{
            bodyparam += `  "${key}": ${value}`;
        }
        if (i < bodyparameters.length - 1) {
            bodyparam += ',';
        }
    }
    bodyparam += '}';
    let bodytenant = 'CA'
    let body = `{"action": "${bodyaction}", "parameters": ${bodyparam}, "tenant": "${bodytenant}"}`
    let strThen = testScript.split("Then")[1]
    let statusCode = strThen.split("code:")[1].split("And")[0].split("Examples:")[0]
    let checkValue = checkValues(strThen);
    let awitPar = null
    if ( testScript.includes('willwait') ) {
        awitPar = testScript.split("willwait")[1].split("seconds")[0]
    }
    let timeOutPar = null
    if ( testScript.includes('timeout') ) {
        let timeOutParStr = testScript.split("timeoutshouldbe")[1].split("seconds")[0]
        timeOutPar = parseInt(timeOutParStr) * 1000
    }
    let parObj = saveParams(steps);
    let resultData = null
    if ( steps.includes('Examples:') ) {
        resultData = examplData(steps)
    }

    return objTC = {
        TC_name: tcName,
        Method: method,
        Url: url,
        Header: JSON.parse(JSON.stringify(header)),
        Body: body,
        StatusCode: statusCode,
        SafaValue: checkValue,
        AwaitPar: awitPar,
        TimeOutPar: timeOutPar,
        SafeParams: parObj,
        TestData: resultData
    };
}

export {dataBaker}