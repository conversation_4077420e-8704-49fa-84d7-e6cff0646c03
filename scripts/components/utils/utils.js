const environment = process.env.ENVIRONMENT || 'staging';

function envUrl(testScript){
    let urlWithoutEnv = testScript.split("url`")[1].split("`And")[0]
    let url = urlWithoutEnv.replace("<env>", environment)
    return url
}
function parsparam(arr){
    let str = '{'
    for (let i = 0; i < arr.length; i++) {
        let [key, value] = arr[i].split(/:(.+)/);
        if (value.includes('${')) {
            value = value.replace(/^"|"$/g, '');
            str += `  "${key}": ${value}`;
        }else{
            str += `  "${key}": ${value}`;
        }
        if (i < arr.length - 1) {
            str += ',';
        }
    }
    str += '}'
    return str
}
function checkValues(strThen){
    let safaV = strThen.split('containthevalue:"')
    let checkValue = {}
    for ( let i=0; i<safaV.length-1; i++) {
        let itemV = safaV[i+1].split('"')[0]
        checkValue[i] = itemV
    }
    return checkValue
}
function saveParams(steps){
    let strSaveP = steps.split("Then")[1]
    let safeP = strSaveP.split('save params "')
    let parObj = {}
    for ( let i=0; i<safeP.length-1; i++) {
        let itemP = safeP[i+1].split('"')[0]
        parObj[i] = itemP
    }
    return parObj
}
function examplData(steps){
    let examplData = steps.split("Examples:")[1]
    let str = examplData.replace(/\t/g, '').replace(/ /g,'')
    let rows = str.split('\n').filter(row => row !== "");
    let keys = rows[0].split("|").filter(element => element !== '')
    let resultData = [];
    for (let i = 1; i < rows.length; i++) {
        let values = rows[i].split("|").filter(value => value !== "");
        let obj = {}
        for ( let k in values ) {
            obj[keys[k]]= values[k]
        }
        resultData.push(obj);
    }
    return resultData
}

export {envUrl, parsparam, checkValues, examplData, saveParams}