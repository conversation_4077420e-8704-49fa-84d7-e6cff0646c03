import file from 'k6/x/file';

const logFilePath = './generated_data/k6_execution.log';

let сounter = function(){
    var i = 1;

    return function(){
        return i++;
    };
}();

function generateTestCaseKey() {
    return `k6-T${сounter().toString().padStart(3, '0')}`;
}

function maskSensitiveData(data) {
    if (!data) return data;

    let sensitiveWords = ["password", "token", "apikey"]; // Add more sensitive keys as needed

    function maskValue(value, key) {
        if (typeof value === "string") {
            if (sensitiveWords.includes(key.toLowerCase())) {
                return "*****"; // Mask entire value for sensitive keys
            }

            // Mask sensitive data inside strings (e.g., token inside Cookie)
            return value.replace(new RegExp(`(${sensitiveWords.join("|")})=.*?([;"]|$)`, "gi"), "$1=*****$2");
        } else if (Array.isArray(value)) {
            return value.map((item) => maskValue(item, key)); // Recursively process arrays
        } else if (typeof value === "object" && value !== null) {
            return maskObject(value); // Recursively process objects
        }
        return value;
    }

    function maskObject(obj) {
        let maskedObj = {};
        for (let key in obj) {
            maskedObj[key] = maskValue(obj[key], key);
        }
        return maskedObj;
    }

    try {
        let jsonData = typeof data === "string" ? JSON.parse(data) : data;
        return JSON.stringify(maskObject(jsonData));
    } catch (e) {
        return data; // Return original data if JSON parsing fails
    }
}


function logRequestData(res, checkResult, testCaseKey = generateTestCaseKey()){
    let resBody = ''
    try{
        resBody = JSON.parse(res.body);
    } catch (error) {
        resBody = res.body;
    }
    let logEntry = {
        "ProjectKey": __ENV.PROJECT_KEY,
        "ScenarioKey": __ENV.K6_FILE_NAME,
        "TestCase": testCaseKey,
        "ResponseCode": res.status,
        "URL": res.request.method + " " + res.url,
        "RequestHeader": maskSensitiveData(res.request.headers),
        "RequestBody": maskSensitiveData(res.request.body),
        "ResponseHeader": maskSensitiveData(res.headers),
        "ResponseBody": maskSensitiveData(resBody),
        "level": (checkResult == true ? 'OK' : 'error'),
        "timestamp": Date.now(),
    }

    const jsonString = JSON.stringify(logEntry);
    file.appendString(logFilePath, jsonString+'\n' );
}

function logSQSEventData(stepItem, testQueue, sqsReg) {
    let logEntry = {
        "ProjectKey": __ENV.PROJECT_KEY,
        "ScenarioKey": __ENV.CYCLE_KEY || __ENV.K6_FILE_NAME,
        "TestCase": stepItem.TC_key,
        "ResponseCode": stepItem.StatusCode,
        "URL": testQueue,
        "RequestBody": JSON.stringify(stepItem.Body),
        "ResponseBody": JSON.stringify(sqsReg),
        "level": 'OK',
        "timestamp": Date.now(),
    }
    const jsonString = JSON.stringify(logEntry);
    file.appendString(logFilePath, jsonString+'\n' );
}

export {logRequestData, logSQSEventData}