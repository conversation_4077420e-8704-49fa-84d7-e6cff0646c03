import {envUrl, checkValues, examplData, saveParams} from "../utils/utils.js"

function apiCall (steps){
    let objTC = new Object();
    let testScript = steps.replace(/ /g,'').replace(/\n/g, '')
    let tcName = steps.split('Scenario "')[1].split('"')[0]
    let url = envUrl(testScript);
    let method = testScript.split("method")[1].split("When")[0].split("And")[0]

    let header = "{}";
    if ( testScript.includes('headers:') ) {
        header = steps.replace(/\n/g, '').split('headers:')[1].split('body')[0].split('"""')[1]
    }
    let body = "{}";
    if ( testScript.includes('body:') ) {
        body = steps.replace(/\n/g, '').split('body:')[1].split('When')[0].split('"""')[1]
    }
    let strThen = testScript.split("Then")[1]
    let statusCode = strThen.split("code:")[1].split("And")[0].split("Examples:")[0]
    let checkValue = checkValues(strThen);
    let awitPar = null

    if ( testScript.includes('willwait') ) {
        awitPar = testScript.split("willwait")[1].split("seconds")[0]
    }
    let parObj = saveParams(steps);
    let resultData = null
    if ( steps.includes('Examples:') ) {
        resultData = examplData(steps)
    }

    return objTC = {
        TC_name: tcName,
        Method: method,
        Url: url,
        Header: JSON.parse(JSON.stringify(header)),
        Body: JSON.parse(JSON.stringify(body)),
        StatusCode: statusCode,
        SafaValue: checkValue,
        AwaitPar: awitPar,
        SafeParams: parObj,
        TestData: resultData
    };
}

export {apiCall}