import {envUrl, parsparam, checkValues, examplData, saveParams} from "../utils/utils.js"

function apiCallV2 (steps){
    let objTC = new Object();
    let testScript = steps.replace(/ /g,'').replace(/\n/g, '')
    let tcName = steps.split('request "')[1].split('"')[0]
    let url = envUrl(testScript);
    let method = testScript.split("method")[1].split("When")[0].split("And")[0]
    let header = "{}";
    if ( testScript.includes('header') ) {
        let headerParam = steps.replace(/\n/g, '').split('header')[1].split('And request body')[0].split("When")[0].split("And ")
        headerParam.shift()
        header = parsparam(headerParam)
    }

    let body = "{}";
    if ( testScript.includes('body') ) {
        let bodyTypeValue = steps.replace(/\n/g, '').split('body')[1].split('When')[0].split('And obj')
        if ( bodyTypeValue[0].replace(/ /g, '') ==  'type1'){
            let bodyV = [...bodyTypeValue];
            bodyV.shift()
            for (let k in bodyV){
                let bodys = bodyV[k].split('And ')
                bodys.shift()
                body = parsparam(bodys)
            }
        }
        if ( bodyTypeValue[0].replace(/ /g, '') ==  'type2'){
            let bodyV = [...bodyTypeValue];
            bodyV.shift()
            body = "["
            for (let k in bodyV){
                let bodys = bodyV[k].split('And ')
                bodys.shift()
                let bodyobj = parsparam(bodys)
                bodyobj += ',\n'
                body = body + bodyobj
            }
            body += "]"
        }
    }

    let strThen = testScript.split("Then")[1]
    let statusCode = strThen.split("code:")[1].split("And")[0].split("Examples:")[0]
    let checkValue = checkValues(strThen);
    let awitPar = null
    if ( testScript.includes('willwait') ) {
        awitPar = testScript.split("willwait")[1].split("seconds")[0]
    }
    let timeOutPar = null
    if ( testScript.includes('timeout') ) {
        let timeOutParStr = testScript.split("timeoutshouldbe")[1].split("seconds")[0]
        timeOutPar = parseInt(timeOutParStr) * 1000
    }
    let parObj = saveParams(steps);

    let resultData = null
    if ( steps.includes('Examples:') ) {
        resultData = examplData(steps)
    }

    return objTC[`Step1`] = {
        TC_name: tcName,
        Method: method,
        Url: url,
        Header: header,
        Body: body,
        StatusCode: statusCode,
        SafaValue: checkValue,
        AwaitPar: awitPar,
        TimeOutPar: timeOutPar,
        SafeParams: parObj,
        TestData: resultData
    };
}

export {apiCallV2}