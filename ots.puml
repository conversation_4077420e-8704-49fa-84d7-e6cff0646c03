@startuml
Title: One-Test-Suite v0.1.4
group Precondition.js

JS->>ZephyrScale: Get Test Cycle info by key
note over JS,ZephyrScale: query Zephyr for the given key ( id ) \nof Test Cycle (get data about the \ncontained Test Cases in it)
ZephyrScale-->>JS: Test Cases from the Test Cycle

loop Test Cases in Test Cycle
JS->>ZephyrScale: Get Test Cases info
note over JS, ZephyrScale: Execute a query for each Test Case \nand get its contents
ZephyrScale-->>JS: Test Cases info
end

note over JS: generate a json file with \nTest Data about each Test Case
JS->JS:Save json file
end

group CreateK6file.js
note over JS: form a K6 script based on the \nreceived data from the json file
JS->JS:Save k6 script
end

JS-->K6:

note over K6: Run k6 script
K6->>DataDog: Sending metrics and logs
K6->K6: Save the test report data \nin a file


K6-->JS:

group Postcondition.js
JS->>ZephyrScale: Send the status of Test Case execution to Zephyr Scale
JS->>DataDog: Sending additional metrics
end
@enduml
