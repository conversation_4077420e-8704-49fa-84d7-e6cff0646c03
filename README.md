## 📋 Table of Contents

1. ✨ [What is this One-Test-Suite ?](#-what-is-this-one-test-suite)
2. 🏛️ [The Solution Architecture](#-the-solution-architecture)
3. 🚀 [Usage](#-usage)
   - [Create TestCases and Test Cycles in Zephyr Scale](#create-testcases-and-test-cycles-in-zephyr-scale--demo)
   - [Run test cycle by pull request](#run-test-cycle-by-pull-request--demo)
   - [Functional testing](#functional-testing--demo)
   - [Performance testing](#performance-testing--demo)
   - [Add step to pipeline run TestCycle after deploy new feature](#Add-step-to-pipeline-run-TestCycle-after-deploy-new-feature)
4. 📈 [Analysis metrics](#-analysis-metrics)
5. 📘 [Documentation](#-documentation)
6. 🔩 [Sequence Diagram](#-sequence-diagram)
7. 🤖 [Support](#-support)
8. ©️ [License](#-license)

---

## <a name="what-is-this-one-test-suite">✨ What is this One-Test-Suite ?</a>
The One-Test-Suite concept focuses on developing a versatile tool that can not only perform functionality and performance testing, but also provide seamless interoperability with different systems. The main goal is to integrate with Zephyr Scale systems to simplify test creation and maintenance, and with Data Dog and Zephyr Scale systems to continuously monitor the status of test cases. This comprehensive approach not only increases the efficiency of performance testing, but also contributes collaboration in the testing ecosystem. As a result, K6 Core becomes a solution that can improve the accuracy and efficiency of performance evaluation, making it an indispensable tool in software development.

## <a name="the-solution-architecture">🏛️️️ The Solution Architecture</a>
### V0.1.2 [drawio](https://app.diagrams.net/#G1DBgh-RX5W77QYX-Cnbso4DTskmzCGJ0p)
<p align="center"><img src="assets/architecture_v0.1.2.png"/></p>

---

## 🚀 Usage

### Create TestCases and Test Cycles in Zephyr Scale ➡️ [Demo](https://drive.google.com/file/d/1sZ0zQEsAZ_IXD-jBGI4Rp5ld98SXiX52/view?resourcekey)

1. Open the Zephyr Scale plug-in in Jira
 - Create TestCases in BDD Gherkin format according to the template.  
 - In the process of describing a TestCase we specify:
   - URL / Method / Header / Body / Expected result.
   - Parameters to be saved for use in next requests.

 - When we use test data ➡️ we should describe it in the Example block.

 - When we do manual execution using GitHub Action ➡️ we can select the environment

2. When we have TestCases ➡️ we can create TestCycle to run automatically using OTS

<p align="center"><img src="assets/zephyr_scale_v0.1.2[1].png"/></p>


### Script API Examples:

<details>
<summary>
  GET
</summary> <br />

```gherkin
Scenario "Ping"  
  Given the API url `https://restful-booker-<env>.herokuapp.com/ping`
  And the method GET
  When I send request to the endpoint
  Then the API should respond with a code: 201
```
</details>

<details>
<summary>
  GET (with test_data)
</summary> <br />

```gherkin
Scenario "Get summary"
  Given the API url `https://restful-booker-<env>.herokuapp.com/${testData.courier_id}/summary`
  And the method GET
  When I send request to the endpoint
  Then the API should respond with a code: 200

  Examples:
  |courier_id              |
  |018af14c-c334-9274-*****|
  |018af14d-c62a-c338-*****|
  |018af14e-a30a-9b06-*****|
  |018af150-01a9-25e0-*****|
  |018af151-3137-da86-*****|
```
</details>

<details>
<summary>
  POST
</summary> <br />

```gherkin
Scenario "Add token"  
  Given the API url `https://restful-booker-<env>.herokuapp.com/auth`
  And the method POST
  And the request headers:
  """
  {
  'Content-Type': 'application/json'
  }
  """
  And the request body:
  """
  {
  'username': 'admin',
  'password': 'password123'
  }
  """
  When I send request to the endpoint
  Then the API should respond with a code: 200
  And from response body save params "token= resBody.token" the newly created user details
```

```gherkin
Scenario "Create booking"  
  Given the API url `https://restful-booker.herokuapp.com/booking`
  And the method POST
  And the request headers:
  """
  {
  'Accept': 'application/json',
  'Content-Type':'application/json'
  }
  """
  And the request body:
  """
 {
  'firstname': 'Ivan',
  'lastname' : 'Smirnov',
  'totalprice' : 111,
  'depositpaid' : true,
  'bookingdates' : {
  'checkin' : '2022-01-01',
  'checkout' : '2023-01-01'
  },
  'additionalneeds' : 'Breakfast'
  }
  """
  When I send request to the endpoint
  Then the API should respond with a code: 200
  And the API response body should contain the value: "resBody.estimate.solver == 'GRAPHHOPPER'"
  And from response body save params "id=resBody.bookingid" the newly created user details
  And from response body save params "firstname=resBody.booking.firstname" the newly created user details
  And from response body save params "lastname=resBody.booking.lastname" the newly created user details
  And from response body save params "checkin=resBody.booking.bookingdates.checkin" the newly created user details
  And from response body save params "checkout=resBody.booking.bookingdates.checkout" the newly created user details
```
</details>

<details>
<summary>
  PUT
</summary> <br />

```gherkin
Scenario "Update booking"  
  Given the API url `https://restful-booker.herokuapp.com/booking/${id}`
  And the method PUT
  And the request headers:
  """
  {
  'Accept': 'application/json',
  'Content-Type': 'application/json',
  'Cookie': `token=${token}`
  }
  """
  And the request body:
  """
 {
  'firstname' : `${firstname}`,
  'lastname' : `${lastname}`,
  'totalprice' : 111,
  'depositpaid' : true,
  'bookingdates' : {
    'checkin' : '2022-01-01',
    'checkout' : '2023-02-01'
  },
  'additionalneeds' : 'Breakfast'
  }
  """
  When I send request to the endpoint
  Then the API should respond with a code: 200
  And the API response body should contain the value: "resBody[0].estimate.durationInMinutes > 0"
  And the API response body should contain the value: "resBody[0].estimate.solver == 'KITT'"
  And from response body save params "checkin=resBody[1].booking.bookingdates.checkin" the newly created user details
```
</details>

<details>
<summary>
  DELETE
</summary> <br />

```gherkin
Scenario "Delete booking"  
  Given the API url `https://restful-booker.herokuapp.com/booking/${id}`
  And the method DELETE
  And the request headers:
  """
  {
  'Accept': 'application/json',
  'Cookie': `token=${token}`
  }
  """
  When I send request to the endpoint
  Then the API should respond with a code: 201
```
</details>

> Note: To use a parameter derived from previous queries in a query, you must use the following condition: In query #1, store the parametric in the temporary variable `token=${token}`. Use the temporary variable in the next request `${testData.token}`.

### Script SQS Examples:

<details>
<summary>
  SQS
</summary> <br />

```gherkin
Scenario "Send msg to SQS"
Given the SQS url `https://sqs.us-east-1.amazonaws.com/${aws_sqs}/<env>_wallet_management_CourierEarningsUpdated`

And the message body:
"""
{
     "Subject": "CourierEarningsUpdated",
     "Message": `{
       \"id\": \"4444-5555-${randomNumberOfSetLength(3)}-ots-${randomNumberInSetRange(100,900)}\",
       \"TimeStamp\": \"${currentTimeStamp()}\",
       \"RaisingComponent\": \"one-test-suite\",
       \"transactionId\": \"e2f4c19a-****-****-****-${randomString(12)}\",
       \"courierId\": \"018c7e6d-ece5-****-****-67**********\",
       \"paymentType\": \"CREDIT_CARD\",
       \"status\": \"DELIVERED\",
       \"total\": 5,
       \"type\": \"DELIVERY\"
     }`
}
  """
Then the API should respond with a code: 200
And from response body save params "messageId=resBody.MessageId" the newly created user details
And the user will wait 5 seconds
```
</details>

<details>
<summary>
  Random, Date and Time methods (helper)
</summary> <br />

```javascript
randomNumberInSetRange(0, 100) => 14

randomNumberOfSetLength(5) => 32413

randomString(7) => gh2mit9

currentDataUTC() => 1716380970

currentTimeStamp() => 2024-05-22T12:29:30.131Z

currentTime() => 12:29:30

currentData() => 2024-05-22

currentDayOfWeek() => 'Sunday' ...or... 'Saturday'
```
</details>

---

### Run test cycle by pull request ➡️ [Demo](https://drive.google.com/file/d/1zBplFxKvGFHLMPLTImOHc03POOc0tqrq/view?usp=drive_link)

When we have a TestCycle with TestCases ➡️ we can run it automatically when creating a pull request.

1. For this we need to activate the block with Zephyr Scale in jira task and add TestCycle to it.
   - When the PR for the Jira task is created, then the TestCycle attached to it will be automatically executed
   - The current status of the automatic execution of the TestCycle is displayed in the Jira Task.

2. Execution details:
   - If we open the gitHub action ➡️ we can see which TestCycles have been run. In this example 1 TestCycle has been added to the Jira task and it has been run, If we add multiple TestCycles to the task ➡️ then they will be run in parallel.
   - Also in the execution details we can analyze the logs.

3. RP details will provide links to the report in Zephyr Scale and Data Dog for each of the TestCycles
   - Zephyr Scale ➡️ we can check the time / date / status of the last run of each TestCase.
   - Data Dog ➡️ we can check the execution details of each request.

<p align="center"><img src="assets/run_test_cycle_by_pull_request_v0.1.2[1].png"/></p>

---

### Functional testing ➡️ [Demo](https://drive.google.com/file/d/1Ig_21hmMvuaCp2oNMObIN_A1p4TpuyV_/view?usp=drive_link)

When we have TestCycle on our project ➡️ we can execute them automatically using GitHub Action.

1. For this we need to:
   - Open GitHub action ➡️ run-testcycle in One-Test-Suite project
   - In the run-workflow section, select the current version (also in the master branch will be the current release)
   - Specify the list of TestCycles we want to run
   - Select environment
   - For functional testing other fields should be left by default. 

If we open workflow execution details ➡️ we can see a list of parallel running test cycles. By opening it we can see the execution steps.

2. During the execution of TestCycle ➡️ actual metrics are transferred to Data Dog in real time.
   Where we can check the execution details for each test case from TestCycle.

3. Zephyr Scale will display the actual status of TestCycle execution

<p align="center"><img src="assets/functional_testing_v0.1.2[1].png"/></p>

---

### Performance testing  ➡️ [Demo](https://drive.google.com/file/d/1sesAYxAHq7IHUZF2F0y3xDw-lZEumhc9/view?usp=sharing)

We can also do performance testing from GitHub Actions using on the created TestCycle.

> Note: In release 0.1.2 the following performance testing types are available: load / stress / endurance. In this example, we will use the endurance test type to execute TestCases with test data.

1. For this we need to:
   - Follow the steps as in functional testing and also fill in additional fields
   - Specify the number of users ➡️ 5
   - Specify test duration ➡️ 30 sec

2. If we open the execution details of the workflow ➡️ Opening a job with the name TestCycle ➡️ we can see the execution steps.
   - Getting test data from TestCase
   - Parameters for performance testing
   - Progress of the endurance testing execution

3. During the execution of performance testing ➡️ the actual metrics are transferred to DD in real time.
   Where we can check the execution details for each test case from TestCycle as well as other metrics.

4. ZS will display the current status of TestCycle execution

<p align="center"><img src="assets/performance_testing_v0.1.2[1].png"/></p>

---
### Add step to pipeline run TestCycle after deploy new feature

1. Add a one-test-suite step to the pipeline startup loop after deploying a new feature.
2. Add the [ots](https://github.je-labs.com/delco-infra-tools/one-test-suite/tree/master/.github/actions) folder to your project in the `.github/actions/ots/` path.
3. Add action secrets and variables in your project

```yaml
 one-test-suite:
    needs: deploy
    runs-on: [ docker-runner, self-hosted ]
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v1
        with:
          node-version: 20.x
      - name: Install dependencies
        run: npm install node-fetch
      - name: afterDeploy input
        env:
          ZEPHYR_SCALE_TOKEN: ${{ secrets.ZEPHYR_SCALE_TOKEN}}
          OTS_TOKEN_API: ${{ secrets.OTS_TOKEN_API }}
          ZEPHYR_SCALE_URL: ${{ vars.ZEPHYR_SCALE_URL }}
          OTS_GITHUB_API_URL: ${{ vars.OTS_GITHUB_API_URL }}
          OTS_WORKFLOW_ID: ${{ vars.OTS_WORKFLOW_ID }}
          GITHUB_REPOSITORY: ${{ github.repository }}
          GITHUB_REF_NAME: ${{ github.ref_name }}
        id: afterDeployInput
        uses: ./.github/actions/ots/
```

---

## 📈 Analysis metrics

### [one-test-suite [release-v0.1.4][stable]](https://app.datadoghq.eu/dashboard/wmk-b9h-pec/k6-core-master?fromUser=false&refresh_mode=sliding&view=spans&from_ts=1716902802057&to_ts=1716903702057&live=true) 


1. Summary
2. Sampler metrics
3. Logs list
4. Logs details

<p align="center"><img src="assets/datadog_v0.1.4[1].png"/></p>

---

### Zephyr Scale
<p align="center"><img src="assets/zephyr_scale_1.png"/></p>

---

### 🔩 SequenceDiagram
<p align="center"><img src="assets/ots_v0_1_4.png"/></p>

---

## 📘 Documentation
[Confluence](https://justeattakeaway.atlassian.net/wiki/spaces/CSI/pages/**********/DataDog+Zephyr+Scale+k6): Demo, roadmap

---

## 🤖 Support

To get help, report bugs, suggest new features, and discuss with others, contact [#team-courier-services-infra](). or please add new story to [Epic](https://justeattakeaway.atlassian.net/browse/CSI-2252)

---

## ©️ License