{"title": "OTS One Test Suite [release-v0.1.7][alpha]", "description": "[[suggested_dashboards]]", "widgets": [{"id": 3819598474076690, "definition": {"type": "note", "content": "![one-test-suite](https://ots-cycle-key-schedule.s3.amazonaws.com/ots_logo1.png)\n\nThe OTS concept focuses on developing a versatile tool that can not only perform functionality and performance testing, but also provide seamless interoperability with different systems. \n\n[**GitHub ↗**](https://github.je-labs.com/delco-infra-tools/one-test-suite)", "background_color": "white", "font_size": "14", "text_align": "left", "vertical_align": "top", "show_tick": false, "tick_pos": "50%", "tick_edge": "left", "has_padding": true}, "layout": {"x": 0, "y": 0, "width": 3, "height": 6}}, {"id": 4935794573777158, "definition": {"title": "Summary", "type": "group", "layout_type": "ordered", "widgets": [{"id": 2400987926316612, "definition": {"title": "Total requests", "title_size": "16", "title_align": "left", "type": "query_value", "requests": [{"formulas": [{"number_format": {"unit": {"label": "req", "type": "custom_unit_label"}}, "formula": "query1"}], "response_format": "scalar", "queries": [{"data_source": "logs", "name": "query1", "indexes": ["*"], "compute": {"aggregation": "count"}, "group_by": [], "search": {"query": "service:zephyrScale $test_cycle_key $project_key status:$status.value @ResponseCode:$response_code.value $type_execution $environment"}, "storage": "hot"}]}], "autoscale": true, "precision": 2, "timeseries_background": {"yaxis": {"include_zero": true}, "type": "area"}}, "layout": {"x": 0, "y": 0, "width": 3, "height": 2}}, {"id": 4162607939244800, "definition": {"title": "Failed requests", "title_size": "16", "title_align": "left", "type": "query_value", "requests": [{"formulas": [{"number_format": {"unit": {"label": "req", "type": "custom_unit_label"}}, "formula": "query1"}], "response_format": "scalar", "queries": [{"data_source": "logs", "name": "query1", "indexes": ["*"], "compute": {"aggregation": "count"}, "group_by": [], "search": {"query": "service:zephyrScale $test_cycle_key $project_key status:error @ResponseCode:$response_code.value $type_execution $environment"}, "storage": "hot"}], "conditional_formats": [{"comparator": ">", "value": 0, "palette": "black_on_light_red"}]}], "autoscale": true, "precision": 2, "timeseries_background": {"yaxis": {"include_zero": true}, "type": "area"}}, "layout": {"x": 3, "y": 0, "width": 3, "height": 2}}, {"id": 8454272422174550, "definition": {"title": "Error rate", "title_size": "16", "title_align": "left", "type": "query_value", "requests": [{"formulas": [{"number_format": {"unit": {"label": "%", "type": "custom_unit_label"}}, "formula": "query3 * 100 / query2"}], "conditional_formats": [{"comparator": ">", "palette": "black_on_light_red", "value": 0}, {"comparator": ">", "palette": "white_on_red", "value": 5}, {"comparator": "=", "value": 0, "palette": "black_on_light_green"}], "response_format": "scalar", "queries": [{"data_source": "logs", "name": "query3", "indexes": ["*"], "compute": {"aggregation": "count"}, "group_by": [], "search": {"query": "service:zephyrScale $test_cycle_key $project_key status:error @ResponseCode:$response_code.value $type_execution $environment"}, "storage": "hot"}, {"data_source": "logs", "name": "query2", "indexes": ["*"], "compute": {"aggregation": "count"}, "group_by": [], "search": {"query": "service:zephyrScale $test_cycle_key $project_key status:$status.value @ResponseCode:$response_code.value $type_execution $environment"}, "storage": "hot"}]}], "autoscale": true, "precision": 0, "timeseries_background": {"yaxis": {"include_zero": true}, "type": "area"}}, "layout": {"x": 6, "y": 0, "width": 3, "height": 2}}, {"id": 8684290128033608, "definition": {"title": "Virtual users", "title_size": "16", "title_align": "left", "show_legend": false, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "type": "timeseries", "requests": [{"formulas": [{"formula": "query1"}], "queries": [{"data_source": "metrics", "name": "query1", "query": "max:k6.vus{source:k6_core,zephyrscalecycle:$test_cycle_key.value,zephyrscaleproject:$project_key.value}"}], "response_format": "timeseries", "style": {"palette": "dog_classic", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}], "yaxis": {"label": "", "scale": "linear", "include_zero": true, "min": "auto", "max": "auto"}, "custom_links": []}, "layout": {"x": 0, "y": 2, "width": 3, "height": 3}}, {"id": 8633587515456892, "definition": {"title": "Error count per sampler", "title_size": "16", "title_align": "left", "type": "toplist", "requests": [{"response_format": "scalar", "queries": [{"data_source": "metrics", "name": "query1", "query": "avg:k6.http_req_failed{expected_response:false} by {group}.as_count()", "aggregator": "sum"}], "formulas": [{"formula": "query1"}], "sort": {"count": 500, "order_by": [{"type": "formula", "index": 0, "order": "desc"}]}}], "style": {}}, "layout": {"x": 3, "y": 2, "width": 6, "height": 3}}]}, "layout": {"x": 3, "y": 0, "width": 9, "height": 6}}, {"id": 940305376822312, "definition": {"title": "Sampler metrics", "type": "group", "layout_type": "ordered", "widgets": [{"id": 2705436750479864, "definition": {"title": "Response time all req", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "horizontal", "legend_columns": ["avg", "min", "max", "value", "sum"], "type": "timeseries", "requests": [{"formulas": [{"formula": "query"}], "queries": [{"query": "avg:k6.group_duration.avg{source:k6_core}", "data_source": "metrics", "name": "query"}], "response_format": "timeseries", "style": {"palette": "dog_classic", "line_type": "solid", "line_width": "normal"}, "display_type": "bars"}], "yaxis": {"scale": "linear", "label": "", "include_zero": true, "min": "auto", "max": "auto"}, "markers": []}, "layout": {"x": 3, "y": 0, "width": 3, "height": 3}}, {"id": 7469460848220904, "definition": {"title": "Response time each req", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "horizontal", "legend_columns": ["avg", "min", "max", "value", "sum"], "type": "timeseries", "requests": [{"formulas": [{"formula": "query"}], "queries": [{"query": "avg:k6.group_duration.avg{source:k6_core} by {group}", "data_source": "metrics", "name": "query"}], "response_format": "timeseries", "style": {"palette": "dog_classic", "line_type": "solid", "line_width": "normal"}, "display_type": "bars"}], "yaxis": {"scale": "linear", "label": "", "include_zero": true, "min": "auto", "max": "auto"}, "markers": []}, "layout": {"x": 6, "y": 0, "width": 3, "height": 3}}, {"id": 2760651260198042, "definition": {"title": "count each req / 10 sec", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "horizontal", "legend_columns": ["avg", "min", "max", "value", "sum"], "type": "timeseries", "requests": [{"formulas": [{"formula": "query"}], "queries": [{"query": "avg:k6.http_reqs{source:k6_core} by {group}.as_count()", "data_source": "metrics", "name": "query"}], "response_format": "timeseries", "style": {"palette": "classic", "line_type": "dashed", "line_width": "normal"}, "display_type": "bars"}]}, "layout": {"x": 9, "y": 0, "width": 3, "height": 3}}, {"id": 6919133769590614, "definition": {"title": "Сount requests by status", "title_size": "16", "title_align": "left", "type": "toplist", "requests": [{"response_format": "scalar", "queries": [{"data_source": "logs", "name": "a", "indexes": ["*"], "compute": {"aggregation": "count"}, "group_by": [{"facet": "@level", "limit": 10, "sort": {"order": "desc", "aggregation": "count"}, "should_exclude_missing": true}], "search": {"query": "service:zephyrScale $test_cycle_key $project_key status:$status.value @ResponseCode:$response_code.value $type_execution $environment"}, "storage": "hot"}], "formulas": [{"formula": "a"}], "sort": {"count": 10, "order_by": [{"type": "formula", "index": 0, "order": "desc"}]}}], "style": {"scaling": "relative"}}, "layout": {"x": 0, "y": 3, "width": 12, "height": 2}}]}, "layout": {"x": 0, "y": 6, "width": 12, "height": 6}}, {"id": 5105320026066720, "definition": {"title": "[Draft] Zephyr Scale statistics by the test cases. ", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "horizontal", "legend_columns": ["avg", "min", "max", "value", "sum"], "time": {"live_span": "1w"}, "type": "timeseries", "requests": [{"formulas": [{"alias": "manual_test_cases", "formula": "query2"}, {"alias": "automate_test_cases", "formula": "query3"}, {"alias": "total_test_cases", "formula": "query1"}], "queries": [{"data_source": "metrics", "name": "query2", "query": "avg:k6_core.zs_info.manual_test_cases{project:$project_key.value}.fill(null)"}, {"data_source": "metrics", "name": "query3", "query": "avg:k6_core.zs_info.automate_test_cases{project:$project_key.value}.fill(null)"}, {"data_source": "metrics", "name": "query1", "query": "avg:k6_core.zs_info.total_test_cases{project:$project_key.value}.fill(null)"}], "response_format": "timeseries", "style": {"palette": "datadog16", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}], "markers": [{"value": "y = 0", "display_type": "error dashed"}]}, "layout": {"x": 0, "y": 0, "width": 6, "height": 4, "is_column_break": true}}, {"id": 4778673496583524, "definition": {"title": "Zephyr Scale. Execution history", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "vertical", "legend_columns": ["min", "max"], "type": "timeseries", "requests": [{"formulas": [{"style": {"palette": "green", "palette_index": 2}, "alias": "Status: Ok", "formula": "query0"}, {"style": {"palette": "warm", "palette_index": 4}, "alias": "Status: Error", "formula": "query1"}, {"style": {"palette": "classic", "palette_index": 1}, "alias": "Status: Info", "formula": "query2"}], "queries": [{"data_source": "logs", "name": "query0", "indexes": ["*"], "compute": {"aggregation": "count", "interval": 10000}, "group_by": [], "search": {"query": "service:zephyrScale $test_cycle_key $project_key status:ok $type_execution $environment"}, "storage": "hot"}, {"data_source": "logs", "name": "query1", "indexes": ["*"], "compute": {"aggregation": "count", "interval": 10000}, "group_by": [], "search": {"query": "service:zephyrScale $test_cycle_key $project_key status:error $type_execution $environment"}, "storage": "hot"}, {"data_source": "logs", "name": "query2", "indexes": ["*"], "compute": {"aggregation": "count", "interval": 10000}, "group_by": [], "search": {"query": "service:zephyrScale $test_cycle_key $project_key status:info $type_execution $environment"}, "storage": "hot"}], "response_format": "timeseries", "style": {"palette": "dog_classic", "line_type": "solid", "line_width": "normal"}, "display_type": "area"}], "markers": [{"value": "y = 0", "display_type": "error dashed"}]}, "layout": {"x": 6, "y": 0, "width": 6, "height": 4}}, {"id": 3770429555125384, "definition": {"title": "Statistics by completed Vusers and Iterations", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "vertical", "legend_columns": ["min", "max", "sum"], "type": "timeseries", "requests": [{"formulas": [{"style": {"palette": "dd20", "palette_index": 2}, "alias": "Iterations", "formula": "query2"}, {"style": {"palette": "purple", "palette_index": 2}, "alias": "VUS", "formula": "query1"}], "queries": [{"data_source": "metrics", "name": "query2", "query": "sum:k6_core.zs_info.iterations{project:csi}"}, {"data_source": "metrics", "name": "query1", "query": "sum:k6_core.zs_info.vUser{project:csi}"}], "response_format": "timeseries", "style": {"palette": "cool", "line_type": "dotted", "line_width": "thick"}, "display_type": "bars"}], "yaxis": {"include_zero": true, "scale": "sqrt"}}, "layout": {"x": 0, "y": 4, "width": 12, "height": 4}}, {"id": 1561422429525650, "definition": {"title": "Logs", "background_color": "orange", "show_title": true, "type": "group", "layout_type": "ordered", "widgets": [{"id": 7874298225709034, "definition": {"title": "Test results", "title_size": "16", "title_align": "left", "requests": [{"response_format": "event_list", "query": {"data_source": "logs_stream", "query_string": "service:zephyrScale $test_cycle_key $project_key status:$status.value @ResponseCode:$response_code.value $type_execution $environment @TestCase:$test_case_key.value ", "indexes": [], "storage": "hot", "sort": {"order": "desc", "column": "timestamp"}}, "columns": [{"field": "status_line", "width": "auto"}, {"field": "timestamp", "width": "auto"}, {"field": "TestCase", "width": "auto"}, {"field": "content", "width": "auto"}]}], "type": "list_stream"}, "layout": {"x": 0, "y": 0, "width": 12, "height": 7}}]}, "layout": {"x": 0, "y": 8, "width": 12, "height": 8}}], "template_variables": [{"name": "type_execution", "prefix": "typeexecution", "available_values": ["manual", "schedule", "pull_request", "after_deploy"], "default": "*"}, {"name": "project_key", "prefix": "zephyrscaleproject", "available_values": ["csi", "sapt", "cfin", "courboar", "delivery", "courser", "cour<PERSON>e"], "default": "*"}, {"name": "environment", "prefix": "zephyrscaleenv", "available_values": ["qa", "stg"], "default": "*"}, {"name": "test_cycle_key", "prefix": "zephyrscalecycle", "available_values": [], "default": "*"}, {"name": "test_case_key", "prefix": "test_case", "available_values": [], "default": "*"}, {"name": "response_code", "prefix": "response_code", "available_values": [], "default": "*"}, {"name": "status", "prefix": "filtered", "available_values": ["error", "ok", "info"], "default": "*"}], "layout_type": "ordered", "notify_list": [], "reflow_type": "fixed"}