{"title": "k6-core [release-v0.1.2][master]", "description": "[[suggested_dashboards]]", "widgets": [{"id": 3819598474076690, "definition": {"type": "note", "content": "![K6 icon](/static/images/logos/k6_large.svg)\n\nThis dashboard provides an overview of your k6 test results so you can track how well your applications perform under stress.\n\nConfigure the Datadog plugin for k6 to start collecting all your test results as logs so you can easily filter and drill down on the results that really matter.\n\n[**Documentation ↗**](https://k6.io/)", "background_color": "white", "font_size": "14", "text_align": "left", "vertical_align": "top", "show_tick": false, "tick_pos": "50%", "tick_edge": "left", "has_padding": true}, "layout": {"x": 0, "y": 0, "width": 3, "height": 6}}, {"id": 4935794573777158, "definition": {"title": "Summary", "type": "group", "layout_type": "ordered", "widgets": [{"id": 2400987926316612, "definition": {"title": "Total requests", "title_size": "16", "title_align": "left", "type": "query_value", "requests": [{"formulas": [{"formula": "query1"}], "response_format": "scalar", "queries": [{"query": "sum:k6.http_reqs{$cycle_key}.as_count()", "data_source": "metrics", "name": "query1", "aggregator": "sum"}]}], "autoscale": true, "custom_unit": "req", "precision": 2, "timeseries_background": {"yaxis": {"include_zero": true}, "type": "area"}}, "layout": {"x": 0, "y": 0, "width": 3, "height": 2}}, {"id": 4162607939244800, "definition": {"title": "Failed requests", "title_size": "16", "title_align": "left", "type": "query_value", "requests": [{"formulas": [{"formula": "default_zero(query1)"}], "response_format": "scalar", "queries": [{"query": "sum:k6.http_req_failed{$cycle_key}.as_count()", "data_source": "metrics", "name": "query1", "aggregator": "sum"}], "conditional_formats": [{"comparator": ">", "value": 0, "palette": "black_on_light_red"}]}], "autoscale": true, "custom_unit": "req", "precision": 2, "timeseries_background": {"type": "area", "yaxis": {"include_zero": true}}}, "layout": {"x": 3, "y": 0, "width": 3, "height": 2}}, {"id": 8454272422174550, "definition": {"title": "Error rate", "title_size": "16", "title_align": "left", "type": "query_value", "requests": [{"formulas": [{"formula": "query3 * 100 / query2"}], "conditional_formats": [{"comparator": ">", "palette": "black_on_light_red", "value": 0}, {"comparator": ">", "palette": "white_on_red", "value": 5}, {"comparator": "=", "value": 0, "palette": "black_on_light_green"}], "response_format": "scalar", "queries": [{"query": "sum:k6.http_reqs{$cycle_key}.as_count()", "data_source": "metrics", "name": "query2", "aggregator": "sum"}, {"query": "sum:k6.http_req_failed{$cycle_key}.as_count()", "data_source": "metrics", "name": "query3", "aggregator": "sum"}]}], "autoscale": true, "custom_unit": "%", "precision": 0, "timeseries_background": {"type": "area", "yaxis": {"include_zero": true}}}, "layout": {"x": 6, "y": 0, "width": 3, "height": 2}}, {"id": 8684290128033608, "definition": {"title": "Virtual users", "title_size": "16", "title_align": "left", "show_legend": false, "type": "timeseries", "requests": [{"response_format": "timeseries", "queries": [{"data_source": "metrics", "name": "query1", "query": "max:k6.vus{$cycle_key}"}], "style": {"palette": "dog_classic", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}], "yaxis": {"label": "", "scale": "linear", "include_zero": true, "min": "auto", "max": "auto"}, "custom_links": []}, "layout": {"x": 0, "y": 2, "width": 3, "height": 3}}, {"id": 8633587515456892, "definition": {"title": "Error count per sampler", "title_size": "16", "title_align": "left", "type": "toplist", "requests": [{"formulas": [{"formula": "query1", "limit": {"count": 500, "order": "desc"}}], "response_format": "scalar", "queries": [{"data_source": "metrics", "name": "query1", "query": "avg:k6.http_req_failed{expected_response:false,$cycle_key} by {group}.as_count()", "aggregator": "sum"}]}], "style": {}}, "layout": {"x": 3, "y": 2, "width": 6, "height": 3}}]}, "layout": {"x": 3, "y": 0, "width": 9, "height": 6}}, {"id": 940305376822312, "definition": {"title": "Sampler metrics", "type": "group", "layout_type": "ordered", "widgets": [{"id": 2705436750479864, "definition": {"title": "Response time all req", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "horizontal", "legend_columns": ["avg", "min", "max", "value", "sum"], "type": "timeseries", "requests": [{"formulas": [{"formula": "query"}], "queries": [{"query": "avg:k6.group_duration.avg{$cycle_key}", "data_source": "metrics", "name": "query"}], "response_format": "timeseries", "style": {"palette": "dog_classic", "line_type": "solid", "line_width": "normal"}, "display_type": "bars"}], "yaxis": {"scale": "linear", "label": "", "include_zero": true, "min": "auto", "max": "auto"}, "markers": []}, "layout": {"x": 3, "y": 0, "width": 3, "height": 3}}, {"id": 7469460848220904, "definition": {"title": "Response time each req", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "horizontal", "legend_columns": ["avg", "min", "max", "value", "sum"], "type": "timeseries", "requests": [{"formulas": [{"formula": "query"}], "queries": [{"query": "avg:k6.group_duration.avg{$cycle_key} by {group}", "data_source": "metrics", "name": "query"}], "response_format": "timeseries", "style": {"palette": "dog_classic", "line_type": "solid", "line_width": "normal"}, "display_type": "bars"}], "yaxis": {"scale": "linear", "label": "", "include_zero": true, "min": "auto", "max": "auto"}, "markers": []}, "layout": {"x": 6, "y": 0, "width": 3, "height": 3}}, {"id": 2760651260198042, "definition": {"title": "count each req / 10 sec", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "horizontal", "legend_columns": ["avg", "min", "max", "value", "sum"], "type": "timeseries", "requests": [{"response_format": "timeseries", "queries": [{"query": "avg:k6.http_reqs{$cycle_key} by {group}.as_count()", "data_source": "metrics", "name": "query"}], "formulas": [{"formula": "query"}], "style": {"palette": "classic", "line_type": "dashed", "line_width": "normal"}, "display_type": "bars"}]}, "layout": {"x": 9, "y": 0, "width": 3, "height": 3}}]}, "layout": {"x": 0, "y": 6, "width": 12, "height": 4}}, {"id": 1561422429525650, "definition": {"title": "Logs: info / pass", "background_color": "green", "show_title": true, "type": "group", "layout_type": "ordered", "widgets": [{"id": 7874298225709034, "definition": {"title": "Test results", "title_size": "16", "title_align": "left", "requests": [{"response_format": "event_list", "query": {"data_source": "logs_stream", "query_string": "service:zephyrScale $cycle_key status:info ", "indexes": [], "storage": "hot", "sort": {"order": "desc", "column": "timestamp"}}, "columns": [{"field": "status_line", "width": "auto"}, {"field": "timestamp", "width": "auto"}, {"field": "content", "width": "auto"}]}], "type": "list_stream"}, "layout": {"x": 0, "y": 0, "width": 12, "height": 7}}]}, "layout": {"x": 0, "y": 0, "width": 12, "height": 8, "is_column_break": true}}, {"id": 4522592412421284, "definition": {"title": "Logs: errors", "background_color": "vivid_pink", "show_title": true, "type": "group", "layout_type": "ordered", "widgets": [{"id": 3155156334413048, "definition": {"title": "Test results", "title_size": "16", "title_align": "left", "requests": [{"response_format": "event_list", "query": {"data_source": "logs_stream", "query_string": "service:zephyrScale $cycle_key status:error ", "indexes": [], "storage": "hot", "sort": {"order": "desc", "column": "timestamp"}}, "columns": [{"field": "status_line", "width": "auto"}, {"field": "timestamp", "width": "auto"}, {"field": "content", "width": "auto"}]}], "type": "list_stream"}, "layout": {"x": 0, "y": 0, "width": 12, "height": 7}}]}, "layout": {"x": 0, "y": 8, "width": 12, "height": 8}}], "template_variables": [{"name": "cycle_key", "prefix": "zephyrscalecycle", "available_values": [], "default": "*"}], "layout_type": "ordered", "notify_list": [], "reflow_type": "fixed"}