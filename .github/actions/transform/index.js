import core from "@actions/core";

try {
  // Get and normalize input
  const cycleKeyRaw = core.getInput("CYCLE_KEY", { required: true });
  const cycleKeyUppercase = cycleKeyRaw.toUpperCase();

  // Convert string to array
  const cycleKeyArray = cycleKeyUppercase.split(",");

  // Format the array as a JSON string
  const cycleKeyJson = JSON.stringify(cycleKeyArray);

  // Log for debugging
  core.info(`Transformed Inputs: ${cycleKeyJson}`);
  console.log("Formatted cycleKeyJson:", cycleKey<PERSON>son);

  // Set outputs
  core.setOutput("cycle_keys", cycleKeyJson.replace(/"/g, '\\"'));
  core.setOutput("cycle_keys_count", cycleKeyArray.length);
} catch (error) {
  core.setFailed(`Action failed to process input: ${error.message}`);
}
