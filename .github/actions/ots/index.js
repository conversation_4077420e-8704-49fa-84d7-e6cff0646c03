import fetch from "node-fetch";

const ownerRepo = process.env.GITHUB_REPOSITORY
const otsToken = process.env.OTS_TOKEN_API
const Auth = process.env.ZEPHYR_SCALE_TOKEN
const workflowsId = process.env.OTS_WORKFLOW_ID
const zephyrScaleUrl = process.env.ZEPHYR_SCALE_URL
const gitHibApiUrl = process.env.OTS_GITHUB_API_URL
const baseBranch=process.env.GITHUB_REF_NAME

console.log(">> ownerRepo: " + ownerRepo)
console.log(">> workflowsId: " + workflowsId)
console.log(">> zephyrScaleUrl: " + zephyrScaleUrl)
console.log(">> gitHibApiUrl: " + gitHibApiUrl)
console.log(">> baseBranch: " + baseBranch)

let headerGit = {
    "Authorization": `Bearer ${otsToken}`,
    "Accept": "application/vnd.github+json",
    "X-GitHub-Api-Version": "2022-11-28",
}

let headerZC = {
    "Content-Type": "application/json",
    'Authorization': Auth,
}

async function executionData() {

    const reqGetPR = await fetch(`${gitHibApiUrl}/${ownerRepo}/pulls?state=closed`, {
        method: "GET",
        headers: headerGit,
    });
    const respPR = await reqGetPR.json();

    let numberPR;
    let titlePR;
    let dataMergedPR = "";

    for (let i = 0; i < 5; i++) {
        if (respPR[i].merged_at > dataMergedPR && respPR[i].base.ref == baseBranch) {
            numberPR = respPR[i].number
            titlePR = respPR[i].title
            dataMergedPR = respPR[i].merged_at
        }
    }

    console.log("vvvvvvvvv==--Pull Request info--==vvvvvvvvvvvv")
    console.log(numberPR)
    console.log(titlePR)
    console.log(dataMergedPR)
    console.log("ʌʌʌʌʌʌʌʌʌ==--Pull Request info--==ʌʌʌʌʌʌʌʌʌʌʌʌ")

    const reqGetCommInPR = await fetch(`${gitHibApiUrl}/${ownerRepo}/pulls/${numberPR}/commits`, {
        method: "GET",
        headers: headerGit,
    });
    const respCommInPR = await reqGetCommInPR.json();


    let arrComm = []

    for (i in respCommInPR) {
        console.log(respCommInPR[i].commit.message)
        arrComm.push((respCommInPR[i].commit.message).split(" ")[0])
    }

    console.log("vvvvvvvvv==--arrComm--==vvvvvvvvvvvv")
    console.log(arrComm);
    console.log("ʌʌʌʌʌʌʌʌʌ==--arrComm--==ʌʌʌʌʌʌʌʌʌʌʌʌ")

    let arrListTcId = []
    for (n in arrComm) {
        const reqGetTCfromJira = await fetch(`${zephyrScaleUrl}/issuelinks/${arrComm[n]}/testcycles`, {
            method: "GET",
            headers: headerZC,
        });

        const tcFromJira = await reqGetTCfromJira.json();
        try {
            for (k in tcFromJira) {
                if (!tcFromJira[k].id == "") {
                    arrListTcId.push(tcFromJira[k].id)
                }
            }
        } catch (error) {
            console.log("No TC");
        }

    }
    console.log("vvvvvvvvv==--arrListTcId--==vvvvvvvvvvvv")
    console.log(arrListTcId);
    console.log("ʌʌʌʌʌʌʌʌʌ==--arrListTcId--==ʌʌʌʌʌʌʌʌʌʌʌʌ")
    let strTcKeys = ""
    for (m in arrListTcId) {
        const reqGetTcKey = await fetch(`${zephyrScaleUrl}/testcycles/${arrListTcId[m]}`, {
            method: "GET",
            headers: headerZC,
        });
        const reqTcKey = await reqGetTcKey.json();
        if (!strTcKeys.includes(reqTcKey.key || reqTcKey.key == undefined)) {
            strTcKeys = strTcKeys + reqTcKey.key + ","
        }
    }
    strTcKeys = strTcKeys.slice(0, -1)
    console.log("vvvvvvvvv==--strTcKeys--==vvvvvvvvvvvv")
    console.log(strTcKeys)
    console.log("ʌʌʌʌʌʌʌʌʌ==--strTcKeys--==ʌʌʌʌʌʌʌʌʌʌʌʌ")

    let data = {
        "ref": "master",
        "inputs":
            {
                "CYCLE_KEY": strTcKeys,
                "environment": baseBranch
            }
    }
    const runGitAction = await fetch(`${gitHibApiUrl}/delco-infra-tools/one-test-suite/actions/workflows/${workflowsId}/dispatches`, {
        method: "POST",
        headers: headerGit,
        body: JSON.stringify(data),
    });
    if (runGitAction.ok) {
        console.log("Test run successfully");
    } else {
        throw new Error("Test NO run");
    }

}

executionData()