import core from "@actions/core";
import fs from "fs";
import path from "path";

try {
    // Get environment name
    const environmentName = core.getInput("ENVIRONMENT", { required: true });
    const folderPath = `./test_cycles_by_projects/${environmentName}/`;

    // Initialize arrays to hold test cycles
    let parallelTestCycles = [];
    let consistentTestCycles = [];

    // Read files in the specified folder
    const files = fs.readdirSync(folderPath);

    files.forEach((file) => {
        const filePath = path.join(folderPath, file);

        // Process JSON files only
        if (path.extname(file) === ".json") {
            try {
                const fileContent = fs.readFileSync(filePath, "utf-8");
                const fileData = JSON.parse(fileContent);

                // Append to arrays if the properties exist and are arrays
                if (Array.isArray(fileData.testCyclesParallel)) {
                    parallelTestCycles.push(...fileData.testCyclesParallel);
                }

                if (Array.isArray(fileData.testCyclesConsistently)) {
                    consistentTestCycles.push(...fileData.testCyclesConsistently);
                }
            } catch (error) {
                core.warning(
                    `Error parsing JSON file "${file}": ${error.message}`
                );
            }
        }
    });

    // Convert arrays to JSON strings and escape for GitHub Actions
    const parallelCyclesJson = JSON.stringify(parallelTestCycles).replace(/"/g, '\\"');
    const consistentCyclesJson = JSON.stringify(consistentTestCycles).replace(/"/g, '\\"');

    // Log for debugging
    core.info(`Parallel Test Cycles: ${parallelCyclesJson}`);
    core.info(`Consistent Test Cycles: ${consistentCyclesJson}`);

    // Set outputs
    core.setOutput("cycle_key_parallel", parallelCyclesJson);
    core.setOutput("cycle_key_consistently", consistentCyclesJson);
} catch (error) {
    core.setFailed(`Failed to process test cycles: ${error.message}`);
}
