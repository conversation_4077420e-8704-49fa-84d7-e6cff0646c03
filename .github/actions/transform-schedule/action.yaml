name: Workflow input transformer
description: This custom action will transform input to stringify output
inputs:
  ENVIRONMENT:
    required: true
    description: "Env name: dev, stag*"
outputs:
  cycle_key_consistently:
    description: This is transformed output in array of string from provided cycle_key_consistently
  cycle_key_parallel:
    description: This is transformed output in array of string from provided cycle_key_parallel
runs:
  using: node20
  main: index.js
