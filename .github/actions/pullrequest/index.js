import fetch from "node-fetch";
import core from "@actions/core";
const Auth = process.env.ZEPHYR_SCALE_TOKEN
const zephyr_scale_url = process.env.ZEPHYR_SCALE_URL

async function executionData() {

    const jiraTaskKey = core.getInput("CYCLE_KEY", { require: true });

    try {
        const response = await fetch(`${zephyr_scale_url}/issuelinks/${jiraTaskKey}/testcycles`, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
                'Authorization': Auth,
            },
        });
        const res = await response.json();
        let cycle_key = "";

        for ( let i=0; res.length > i; i++ ){
            console.log(res[i].id)
            const response = await fetch(`${zephyr_scale_url}testcycles/${res[i].id}`, {
                method: "GET",
                headers: {
                    "Content-Type": "application/json",
                    'Authorization': Auth,
                },
            });
            const resp = await response.json();
            if ( i < res.length-1){
                cycle_key = cycle_key + resp.key+','
            }
            else{
                cycle_key = cycle_key + resp.key
            }
        }
        console.log(cycle_key)

        core.info(
            `Inputs to pullrequest ${JSON.stringify({
                cycle_key
            })}`
        );
        const cycle_keyArray = cycle_key.split(",");
        const project_keyArray = cycle_key.split("-")[0].toString();
        core.setOutput("project_key", `['${project_keyArray}']`);
        console.log("=======")
        console.log(cycle_keyArray)
        console.log("=======")
        const cycle_keyInputStringified = cycle_keyArray.map(_cycle_key => `'${_cycle_key}'`).toString();
        const cycle_keyInputStringifiedLowerCase = cycle_keyArray.map(_cycle_key => `'${_cycle_key}'`).toString().toLowerCase();
        core.setOutput("cycle_key", `[${cycle_keyInputStringified}]`);
        core.setOutput("cycle_key_lower_case", `[${cycle_keyInputStringifiedLowerCase}]`);
        console.log("-------")
        console.log(cycle_keyInputStringified)
        console.log(cycle_keyInputStringifiedLowerCase)
        console.log("-------")
    } catch (error) {
        core.setFailed(
            `Action failed to transform input with error: ${error.message}`
        );
    }
}

executionData()
