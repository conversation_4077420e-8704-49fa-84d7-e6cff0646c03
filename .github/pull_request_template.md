### 📝 Description
- please describe the purpose of this pull request ...

### 🚀 New Features

📈 DataDog:

[[CSI-xxx](https://justeattakeaway.atlassian.net/browse/CSI-xxx)][ots][vx.x.x][<PERSON> Dog] [<PERSON>]Send logs ...

🔧 one-test-suite:
- List of tasks from jira ...

📂 Zephyr Scale:
- List of tasks from jira ...

📖 Documentation:
- List of tasks from jira ...

drawio: [one-test-suite v0.1.8](https://app.diagrams.net/#G1DBgh-RX5W77QYX-Cnbso4DTskmzCGJ0p#%7B%22pageId%22%3A%22uHVqOIsGg6EfYJMPX6B2%22%7D)

Confluence: [One-Test-Suite [OTS]](https://justeattakeaway.atlassian.net/wiki/spaces/CSI/pages/6511100401/One-Test-Suite+OTS)

Presentation: [OTS [release 0.1.6] August 2024 (v1)](https://docs.google.com/presentation/d/1I9bQ3GgmnKeWncAYVmVO-AmkxoAWHe3NaM-AJDOgRqA/edit#slide=id.p)

♻️ TestCase coverage:
- List of tasks from jira ...

🔁 CI
- List of tasks from jira ...

🔩 Refactor
- List of tasks from jira ...

### ✅ Checklist checks
- [ ] I have tested the changes locally and in run workflow and verified that they work as intended.
- [ ] I have verified that after the automatic execution Test Suite -> metrics are correctly displayed in DataDog.
- [ ] I have verified that after the automatic execution Test Suite -> metrics are correctly displayed in Zephyr Scale.
- [ ] I made sure that after running multiple TestCycles at the same time, they are executed in parallel -> metrics are delivered and displayed correctly for each TestCycle in DataDog. The current status of the run is displayed in Zephyr Scale.
- [ ] I have verified that OTS is doing the correct execution -> functional testing
- [ ] I have verified that OTS is doing the correct execution -> performance testing

### ❎ Checklist deploy
- [ ] Check the project structure (code) for cleanliness before deploying to master
- [ ] Update -> Epic in jira for the current release
- [ ] Update -> README
- [ ] Update -> Sequence diagram
- [ ] Update -> Confluence: [one-test-suite](https://justeattakeaway.atlassian.net/wiki/spaces/CSI/pages/6511100401/One-Test-Suite+OTS)
- [ ] Uploaded and saved the new version of the Data Dog dashboard to the project at the path: `one-test-suite/datadog/dashboards`
- [ ] Update the list of test cycles by schedule
- [ ] Update -> k6_scripts

### 🚢 Successful run action:
- Link ...

### 🐶 DataDog: [one-test-suite [release-vx.x.x][stable]](https://app.datadoghq.eu/dashboard/wmk-b9h-pec/k6-core-master?fromUser=false&refresh_mode=sliding&view=spans&from_ts=1715218224129&to_ts=1715219124129&live=true)