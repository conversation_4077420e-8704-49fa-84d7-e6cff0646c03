name: run-zephyr-scale-testcycles

on:
  workflow_dispatch:
    inputs:
      CYCLE_KEY:
        description: "ZephyrScale cycle key's"
        required: true
        default: 'xxx-x'
        type: string
      EXECUTION_TYPE:
        description: "Execution type"
        required: true
        default: "parallel"
        type: choice
        options:
          - parallel
          - consistently
      ENVIRONMENT:
        description: 'Environment'
        required: false
        default: "staging"
        type: choice
        options:
          - dev
          - staging
          - stageu1
          - staguk
          - stagaus
      OPTIONS:
        description: 'Options'
        required: false
        type: string
        default: '{"vus": 1, "iterations": 1}'
      USING_TEST_DATA:
        description: 'Using test data from Examples: Sequentially'
        type: boolean
        default: true
  workflow_call:
    inputs:
      CYCLE_KEY:
        default: 'xxx-x'
        required: true
        type: string
      ENVIRONMENT:
        default: 'staging'
        type: string
      OPTIONS:
        default: '{"vus": 1, "iterations": 1}'
        required: false
        type: string

jobs:
  precondition:
    runs-on: [ docker-runner, self-hosted ]
    outputs:
      cycle_keys: ${{ steps.set-matrix.outputs.cycle_keys }}
      cycle_keys_count: ${{ steps.set-matrix.outputs.cycle_keys_count }}
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v1
        with:
          node-version: 20.x
      - name: Install dependencies
        run: npm install
      - name: transform input
        id: transformInput
        uses: ./.github/actions/transform/
        with:
          CYCLE_KEY: ${{ inputs.CYCLE_KEY }}
      - id: set-matrix
        run: |
          echo "cycle_keys=${{ steps.transformInput.outputs.cycle_keys }}" >> $GITHUB_OUTPUT
          echo "cycle_keys_count=${{ steps.transformInput.outputs.cycle_keys_count }}" >> $GITHUB_OUTPUT

  run-test-cycle-consistently:
    if: ${{ inputs.EXECUTION_TYPE == 'consistently' }}
    needs: precondition
    name: "${{ matrix.CYCLE_KEY }}"
    strategy:
      fail-fast: false
      matrix:
        CYCLE_KEY: ${{fromJSON( needs.precondition.outputs.cycle_keys )}}
      max-parallel: 1

    uses: ./.github/workflows/execute-k6-flow.yaml
    with:
      IDENTIFIER: ${{ matrix.CYCLE_KEY }}
      IDENTIFIER_TYPE: 'cycle_key'
      TYPE_EXECUTION: manual
      ENVIRONMENT: ${{ inputs.ENVIRONMENT }}
      OPTIONS: ${{ inputs.OPTIONS }}
      USING_TEST_DATA: ${{ inputs.USING_TEST_DATA }}
    secrets: inherit

  run-test-cycle-parallel:
    if: ${{ inputs.EXECUTION_TYPE != 'consistently' }}
    needs: precondition
    name: "${{ matrix.CYCLE_KEY }}"
    strategy:
      fail-fast: false
      matrix:
        CYCLE_KEY: ${{fromJSON( needs.precondition.outputs.cycle_keys )}}

    uses: ./.github/workflows/execute-k6-flow.yaml
    with:
      IDENTIFIER: ${{ matrix.CYCLE_KEY }}
      IDENTIFIER_TYPE: 'cycle_key'
      TYPE_EXECUTION: manual
      ENVIRONMENT: ${{ inputs.ENVIRONMENT }}
      OPTIONS: ${{ inputs.OPTIONS }}
      USING_TEST_DATA: ${{ inputs.USING_TEST_DATA }}
    secrets: inherit