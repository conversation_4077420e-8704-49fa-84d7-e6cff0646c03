name: schedule-run

on:
  workflow_call:
    inputs:
      ENVIRONMENT:
        required: true
        type: string

jobs:
  precondition:
    runs-on: [ docker-runner, self-hosted ]
    outputs:
      cycle_key_consistently: ${{ steps.set-matrix.outputs.cycle_key_consistently }}
      cycle_key_parallel: ${{ steps.set-matrix.outputs.cycle_key_parallel }}
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v1
        with:
          node-version: 20.x
      - name: Install dependencies
        run: npm install
      - name: transform input
        id: transformInput
        uses: ./.github/actions/transform-schedule/
        with:
          ENVIRONMENT: ${{inputs.ENVIRONMENT}}
      - id: set-matrix
        run: |
          echo "cycle_key_consistently=${{ steps.transformInput.outputs.cycle_key_consistently }}" >> $GITHUB_OUTPUT
          echo "cycle_key_parallel=${{ steps.transformInput.outputs.cycle_key_parallel }}" >> $GITHUB_OUTPUT


  run-parallel:
    needs: precondition
    name: "${{ matrix.CYCLE_KEY }}"
    if: ${{ needs.precondition.outputs.cycle_key_parallel != '[]' }}
    strategy:
      fail-fast: false
      matrix:
        CYCLE_KEY: ${{fromJSON( needs.precondition.outputs.cycle_key_parallel )}}
    uses: ./.github/workflows/execute-k6-flow.yaml
    with:
      IDENTIFIER: ${{ matrix.CYCLE_KEY }}
      IDENTIFIER_TYPE: 'cycle_key'
      TYPE_EXECUTION: schedule
      ENVIRONMENT: ${{ inputs.ENVIRONMENT }}
      USING_TEST_DATA: true
    secrets: inherit

  run-consistently:
    needs: precondition
    name: "${{ matrix.CYCLE_KEY }}"
    if: ${{ needs.precondition.outputs.cycle_key_consistently != '[]' }}
    strategy:
      fail-fast: false
      matrix:
        CYCLE_KEY: ${{fromJSON( needs.precondition.outputs.cycle_key_consistently)}}
      max-parallel: 1
    uses: ./.github/workflows/execute-k6-flow.yaml
    with:
      IDENTIFIER: ${{ matrix.CYCLE_KEY }}
      IDENTIFIER_TYPE: 'cycle_key'
      TYPE_EXECUTION: schedule
      ENVIRONMENT: ${{ inputs.ENVIRONMENT }}
      USING_TEST_DATA: true
    secrets: inherit