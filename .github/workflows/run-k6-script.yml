name: run-k6-script

on:
  workflow_dispatch:
    inputs:
      PROJECT_KEY:
        description: "Project key"
        required: true
        default: 'xxx'
        type: string
      K6_FILE_NAME:
        description: "k6 script name"
        required: true
        default: 'k6_script.js'
        type: string
      ENVIRONMENT:
        description: 'Environment'
        required: false
        default: "staging"
        type: choice
        options:
          - dev
          - staging
          - stageu1
          - staguk
          - stagaus
      OPTIONS:
        description: 'Options'
        required: false
        type: string
        default: '{"vus": 1, "iterations": 1}'
      ENV_VARS:
        description: 'Additional env vars'
        required: false
        type: string
        default: '{}'

jobs:
  precondition:
    runs-on: [ docker-runner, self-hosted ]
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Create .github_additional_envs File
        run: |
          mkdir -p tmp
          echo '${{ inputs.ENV_VARS }}' | jq -r 'to_entries | map("\(.key | ascii_upcase)=\(.value)") | .[]' > tmp/.github_additional_envs

      - name: Display Created File
        run: cat tmp/.github_additional_envs

      - name: Upload Environment File as Artifact
        uses: actions/upload-artifact@v3
        with:
          name: github_additional_envs
          path: tmp/.github_additional_envs

  run-your-script:
    needs: precondition  # Ensure it waits for artifact
    uses: ./.github/workflows/execute-k6-flow.yaml
    with:
      IDENTIFIER: ${{ inputs.PROJECT_KEY }}
      IDENTIFIER_TYPE: "project_key"
      K6_FILE_NAME: ${{ inputs.K6_FILE_NAME }}
      ENVIRONMENT: ${{ inputs.ENVIRONMENT }}
      OPTIONS: ${{ inputs.OPTIONS }}
      USE_ENV_FILE: true
    secrets: inherit
