name: run-testcycle-pr-automatically

on:
  pull_request:
    branches: [ alpha ]

jobs:
  precondition:
    runs-on: [ docker-runner, self-hosted ]
    outputs:
      cycle_key: ${{ steps.set-matrix.outputs.cycle_key }}
    env:
      ZEPHYR_SCALE_TOKEN: ${{ secrets.ZEPHYR_SCALE_TOKEN}}
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v1
        with:
          node-version: 20.x
      - name: Install dependencies
        run: npm install
      - name: pullrequest input
        id: pullrequestInput
        uses: ./.github/actions/pullrequest/
        with:
          CYCLE_KEY: ${{ github.head_ref }}
      - id: set-matrix
        run: |
          echo "::set-output name=cycle_key::${{ steps.pullrequestInput.outputs.cycle_key }}"
          echo "${{ github.head_ref }}"

  run-test-cycle:
    needs: precondition
    runs-on: [ docker-runner, self-hosted ]
    name: "${{ matrix.CYCLE_KEY }}"
    strategy:
      fail-fast: false
      matrix:
        CYCLE_KEY: ${{fromJSON( needs.precondition.outputs.cycle_key )}}
    steps:
      - name: Set Project key
        id: set_project_key
        run: |
          project_key=$(echo "${{ matrix.CYCLE_KEY }}" | grep -o "^[^-]*")
          echo "PROJECT_KEY=$project_key" >> "$GITHUB_ENV"
      - uses: actions/checkout@v3
      - name: Docker Agent
        env:
          DD_API_KEY: ${{ secrets.DATADOG_API_KEY }}
          CYCLE_KEY: ${{ matrix.CYCLE_KEY }}
        run: |
          set -o allexport && source ${PWD}/.env/.github_actions && DOCKER_CONTENT_TRUST=1 \
            docker run -d --network bridge \
            --name datadog-agent \
            -v /var/run/docker.sock:/var/run/docker.sock:ro \
            -v /proc/:/host/proc/:ro \
            -v /sys/fs/cgroup/:/host/sys/fs/cgroup:ro \
            -v ${PWD}/generated_data:/generated_data \
            -v ${PWD}/datadog/conf.d:/etc/datadog-agent/conf.d/ \
            -e DD_SITE="datadoghq.eu" \
            -e DD_API_KEY=DATADOG_API_KEY \
            -e DD_TAGS="zephyrScaleCycle:$CYCLE_KEY source:ots zephyrScaleEnv:${{ inputs.environment }}" \
            -e DD_DOGSTATSD_NON_LOCAL_TRAFFIC=1 \
            -e DD_LOG_ENABLED=true \
            -p 8125:8125/udp \
            datadog/agent:$DD_AGENT_VERSION
      - name: Run test cycle
        env:
          CYCLE_KEY: ${{ matrix.CYCLE_KEY }}
          THREADS_USERS: ${{ inputs.threads_users }}
          DURATION: ${{ inputs.duration }}
          ENVIRONMENT: staging
          ITERATIONS: ${{ inputs.iterations }}
          ZEPHYR_SCALE_TOKEN: ${{ secrets.ZEPHYR_SCALE_TOKEN}}
        run: |
          set -o allexport && source ${PWD}/.env/.github_actions && docker run --network host \
            --name k6-node \
            -v ${PWD}:/app \
            -e K6_STATSD_ENABLE_TAGS=true \
            -e CYCLE_KEY=$CYCLE_KEY \
            -e THREADS_USERS=$THREADS_USERS \
            -e DURATION=$DURATION \
            -e ENVIRONMENT=$ENVIRONMENT \
            -e ITERATIONS=$ITERATIONS \
            -e ZEPHYR_SCALE_TOKEN="$ZEPHYR_SCALE_TOKEN" \
            -e DATA_TEST_LIB_LAMBDA_DEV_URL=${{ secrets.DATA_TEST_LIB_LAMBDA_DEV_URL }} \
            -e DATA_TEST_LIB_LAMBDA_STAGING_URL=${{ secrets.DATA_TEST_LIB_LAMBDA_STAGING_URL }} \
            -e DATA_TEST_LIB_LAMBDA_STAGEU1_URL=${{ secrets.DATA_TEST_LIB_LAMBDA_STAGEU1_URL }} \
            -e DATA_TEST_LIB_LAMBDA_STAGUK_URL=${{ secrets.DATA_TEST_LIB_LAMBDA_STAGUK_URL }} \
            -e DATA_TEST_LIB_LAMBDA_STAGAUS_URL=${{ secrets.DATA_TEST_LIB_LAMBDA_STAGAUS_URL }} \
            -e SLACK_WEBHOOK_UNIVERSAL_OTS=${{ secrets.SLACK_WEBHOOK_UNIVERSAL_OTS }} \
            -e SLACK_WEBHOOK_SAPT_OTS=${{ secrets.SLACK_WEBHOOK_SAPT_OTS }} \
            -e SLACK_WEBHOOK_CFIN_OTS=${{ secrets.SLACK_WEBHOOK_CFIN_OTS }} \
            -e SLACK_WEBHOOK_COURBOAR_OTS=${{ secrets.SLACK_WEBHOOK_COURBOAR_OTS }} \
            -e SLACK_WEBHOOK_DELIVERY_OTS=${{ secrets.SLACK_WEBHOOK_DELIVERY_OTS }} \
            -e SLACK_WEBHOOK_COURSER_OTS=${{ secrets.SLACK_WEBHOOK_COURSER_OTS }} \
            -e SLACK_WEBHOOK_COURQE_OTS=${{ secrets.SLACK_WEBHOOK_COURQE_OTS }} \
            --env-file ${PWD}/.env/.github_actions \
            docker.cd.je-labs.com/k6-core:$K6_CORE_VERSION "./entrypoint_cycle.sh"
      - name: Wait for Agent to write logs...
        run: |
          sleep 10
        shell: bash
      - name: Update PR
        uses: actions/github-script@v6
        with:
          github-token: ${{secrets.GITHUB_TOKEN}}
          script: |
            const cycle_key_lowercase = "${{ matrix.CYCLE_KEY }}".toLowerCase();
            const output = `
              You can find a link to the DataDog and Zephyr Scale reports for TestCycle: ${{ matrix.CYCLE_KEY }} below.

              | Name            | Link                                                                                                     |
              | --------------- | -------------------------------------------------------------------------------------------------------- |
              | Commit          | ${{ github.event.pull_request.head.sha }}                                                                |
              | Zephyr Scale    | [Link TestCycle](https://justeattakeaway.atlassian.net/projects/${{ env.PROJECT_KEY }}?selectedItem=com.atlassian.plugins.atlassian-connect-plugin:com.kanoah.test-manager__main-project-page#!/testPlayer/${{ matrix.CYCLE_KEY }}) |
              | DataDog Report  | [Link Report](https://app.datadoghq.eu/dashboard/q93-6v3-6av/k6-core?refresh_mode=sliding&tpl_var_cycle_key%5B0%5D=${cycle_key_lowercase}&view=spans&from_ts=1707315757738&to_ts=1707316657738&live=true) |
              `;

            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: output
            });
