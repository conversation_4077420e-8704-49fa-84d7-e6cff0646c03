name: schedule-init

on:
  schedule:
    - cron: '0 11/12 * * *'

jobs:
  dev:
    uses: ./.github/workflows/schedule-run.yml
    with:
      ENVIRONMENT: 'dev'
    secrets: inherit

  staging:
    uses: ./.github/workflows/schedule-run.yml
    with:
      ENVIRONMENT: 'staging'
    secrets: inherit

  stagaus:
    uses: ./.github/workflows/schedule-run.yml
    with:
      ENVIRONMENT: 'stagaus'
    secrets: inherit  

  stageu1:
    uses: ./.github/workflows/schedule-run.yml
    with:
      ENVIRONMENT: 'stageu1'
    secrets: inherit    
  
  staguk:
    uses: ./.github/workflows/schedule-run.yml
    with:
      ENVIRONMENT: 'staguk'
    secrets: inherit      