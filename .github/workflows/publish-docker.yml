name: publish-image

on:
  workflow_dispatch:

jobs:

  publish-docker-image:
    uses: github-actions/pipelines/.github/workflows/publish-docker.yml@v1
    secrets: inherit
    with:
      docker-image: k6-core
      version-prefix: '1.0'
  tag:
    needs: [ publish-docker-image ]
    uses: github-actions/pipelines/.github/workflows/publish-version.yml@v1
    secrets: inherit
    with:
      version-prefix: '1.0'
    if: ${{ github.event.repository.fork == false && github.event_name != 'pull_request' }}