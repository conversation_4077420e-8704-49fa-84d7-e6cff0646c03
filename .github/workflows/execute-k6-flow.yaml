name: 'execute-k6-flow'

on:
  workflow_call:
    inputs:
      IDENTIFIER:
        required: true
        type: string
      IDENTIFIER_TYPE:
        description: "Identifier types: cycle_key, project_key"
        required: false
        default: "cycle_key"
        type: string
      TYPE_EXECUTION:
        required: false
        type: string
      ENVIRONMENT:
       required: false
       default: "staging"
       type: string
      OPTIONS:
        default: '{"vus": 1, "iterations": 1}'
        required: false
        type: string
      USING_TEST_DATA:
       required: false
       default: true
       type: boolean
      K6_FILE_NAME:
        description: "k6 script name"
        required: false
        type: string
      USE_ENV_FILE:
        description: "should be download additional env file"
        required: false
        default: false
        type: boolean

jobs:
  run:
    name: ${{ inputs.IDENTIFIER }}
    runs-on: [ docker-runner, self-hosted ]
    steps:
      - uses: actions/checkout@v3
      - name: Download Additional Environment File
        if: ${{ inputs.USE_ENV_FILE }}
        uses: actions/download-artifact@v3
        with:
          name: github_additional_envs
          path: tmp
      - name: Set Project key
        shell: bash
        id: set_project_key
        run: |
          if [[ "${{ inputs.IDENTIFIER_TYPE }}" == "project_key" ]]; then
            echo "PROJECT_KEY=${{ inputs.IDENTIFIER }}" >> "$GITHUB_ENV"
          else
            project_key=$(echo "${{ inputs.IDENTIFIER }}" | grep -o "^[^-]*")
            echo "PROJECT_KEY=$project_key" >> "$GITHUB_ENV"
            cycle_or_case=$(echo "${{ inputs.IDENTIFIER }}" | grep -oP '(?<=-)[RT]' || echo "ERROR: WRONG ZEPHYR SCALE KEY. Expected Test Case Key or Test Cycle Key" )
            echo "CYCLE_OR_CASE=$cycle_or_case" >> "$GITHUB_ENV"
          fi
      - name: Define DataDog Agent tags and entrypoint script
        shell: bash
        run: |
          if [[ "${{ inputs.IDENTIFIER_TYPE }}" == "project_key" ]]; then
            dd_tags="typeExecution:k6-script source:ots zephyrScaleProject:${{ inputs.IDENTIFIER }}"
            entrypoint_path="./entrypoint_k6_script.sh"
          else
            cycle_or_case=${{ env.CYCLE_OR_CASE }}
            if [[ $cycle_or_case == 'R' ]]; then
              dd_tags='typeExecution:${{ inputs.TYPE_EXECUTION }} zephyrScaleProject:${{ env.PROJECT_KEY }} zephyrScaleCycle:${{ inputs.IDENTIFIER }} source:ots zephyrScaleEnv:${{ inputs.ENVIRONMENT }} '
              entrypoint_path='./entrypoint_cycle.sh'
            elif [[ $cycle_or_case == 'T' ]]; then
              dd_tags='typeExecution:${{ inputs.TYPE_EXECUTION}} zephyrScaleProject:${{ env.PROJECT_KEY }} zephyrScaleMultiTestCase:${{ inputs.IDENTIFIER }} source:ots zephyrScaleEnv:${{ inputs.ENVIRONMENT }} '
              entrypoint_path='./entrypoint_case.sh'
            else
              echo "$cycle_or_case"
              exit 1
            fi
          fi
          echo "DD_TAGS=$dd_tags" >> "$GITHUB_ENV"
          echo "ENTRYPOINT_PATH=$entrypoint_path" >> "$GITHUB_ENV"
      - name: Docker Agent
        shell: bash
        run: |
          set -o allexport && source ${PWD}/.env/.github_actions && docker run -d --network bridge \
            --name datadog-agent \
            -v /var/run/docker.sock:/var/run/docker.sock:ro \
            -v /proc/:/host/proc/:ro \
            -v /sys/fs/cgroup/:/host/sys/fs/cgroup:ro \
            -v ${PWD}/generated_data:/generated_data \
            -v ${PWD}/datadog/conf.d:/etc/datadog-agent/conf.d/ \
            -e DD_SITE="datadoghq.eu" \
            -e DD_API_KEY=${{ secrets.DATADOG_API_KEY }} \
            -e DD_TAGS="${{ env.DD_TAGS }}" \
            -e DD_DOGSTATSD_NON_LOCAL_TRAFFIC=1 \
            -e DD_LOG_ENABLED=true \
            -p 8125:8125/udp \
            datadog/agent:$DD_AGENT_VERSION
      - name: Get AWS environment
        run: |
          if [[ "${{ inputs.ENVIRONMENT }}" == "dev" ]]; then
            aws_env="us-east-1-pdv-qa-1"
          elif [[ "${{ inputs.ENVIRONMENT }}" == "staging" ]]; then
            aws_env="us-east-1-pdv-staging-1"
          elif [[ "${{ inputs.ENVIRONMENT }}" == "staguk" ]]; then
            aws_env="eu-west-1-pdv-staging-2"
          elif [[ "${{ inputs.ENVIRONMENT }}" == "stagaus" ]]; then
            aws_env="ap-northeast-1-pdv-staging-1"
          elif [[ "${{ inputs.ENVIRONMENT }}" == "stageu1" ]]; then
            aws_env="eu-central-1-pdv-staging-1"
          fi
          echo "$aws_env"
          echo "AWS_ENV=$aws_env" >> "$GITHUB_ENV"
      - name: Set AWS envs
        uses: github-actions/pipelines/actions/setup-ci@v1
        with:
          app: ots
          aws-env: ${{ env.AWS_ENV }}
      - name: Configure AWS credentials
        id: aws_creds
        uses: github-actions/configure-aws-creds@v1
        with:
          role-to-assume: arn:aws:iam::${{ env.AWS_ACCOUNT_ID }}:role/pipeline-ci
          role-session-name: ots
      - name: Format option JSON input
        run: |
          OPTIONS_ESCAPED_JSON=$(echo '${{ inputs.OPTIONS }}' | jq -c . | sed 's/"/\\"/g')
          echo "OPTIONS_ESCAPED_JSON=${OPTIONS_ESCAPED_JSON}" >> $GITHUB_ENV
      - name: Run test cycle
        shell: bash
        run: |
          if [ -f tmp/.github_additional_envs ]; then
            ENV_FILE_OPTION="--env-file tmp/.github_additional_envs"
          else
            ENV_FILE_OPTION=""
          fi
          set -o allexport && \
          source ${PWD}/.env/.github_actions && \
          docker run --network host \
            --name k6-node \
            -v ${PWD}:/app \
            -e K6_STATSD_ENABLE_TAGS=true \
            -e DATADOG_API_KEY=${{ secrets.DATADOG_API_KEY }} \
            -e CYCLE_KEY=${{ inputs.IDENTIFIER }} \
            -e ENVIRONMENT=${{ inputs.ENVIRONMENT }} \
            -e OPTIONS="${{ env.OPTIONS_ESCAPED_JSON }}" \
            -e USING_TEST_DATA=${{ inputs.USING_TEST_DATA }} \
            -e ZEPHYR_SCALE_TOKEN="${{ secrets.ZEPHYR_SCALE_TOKEN}}" \
            -e AWS_ACCESS_KEY_ID="${{ env.AWS_ACCESS_KEY_ID }}"  \
            -e AWS_SECRET_ACCESS_KEY="${{ env.AWS_SECRET_ACCESS_KEY }}" \
            -e AWS_SESSION_TOKEN="${{ env.AWS_SESSION_TOKEN }}" \
            -e AWS_REGION=${{ env.AWS_REGION }} \
            -e DATA_TEST_LIB_LAMBDA_DEV_URL=${{ secrets.DATA_TEST_LIB_LAMBDA_DEV_URL }} \
            -e DATA_TEST_LIB_LAMBDA_STAGING_URL=${{ secrets.DATA_TEST_LIB_LAMBDA_STAGING_URL }} \
            -e DATA_TEST_LIB_LAMBDA_STAGEU1_URL=${{ secrets.DATA_TEST_LIB_LAMBDA_STAGEU1_URL }} \
            -e DATA_TEST_LIB_LAMBDA_STAGUK_URL=${{ secrets.DATA_TEST_LIB_LAMBDA_STAGUK_URL }} \
            -e DATA_TEST_LIB_LAMBDA_STAGAUS_URL=${{ secrets.DATA_TEST_LIB_LAMBDA_STAGAUS_URL }} \
            -e SLACK_WEBHOOK_UNIVERSAL_OTS=${{ secrets.SLACK_WEBHOOK_UNIVERSAL_OTS }} \
            -e SLACK_WEBHOOK_SAPT_OTS=${{ secrets.SLACK_WEBHOOK_SAPT_OTS }} \
            -e SLACK_WEBHOOK_CFIN_OTS=${{ secrets.SLACK_WEBHOOK_CFIN_OTS }} \
            -e SLACK_WEBHOOK_COURBOAR_OTS=${{ secrets.SLACK_WEBHOOK_COURBOAR_OTS }} \
            -e SLACK_WEBHOOK_DELIVERY_OTS=${{ secrets.SLACK_WEBHOOK_DELIVERY_OTS }} \
            -e SLACK_WEBHOOK_COURSER_OTS=${{ secrets.SLACK_WEBHOOK_COURSER_OTS }} \
            -e SLACK_WEBHOOK_COURQE_OTS=${{ secrets.SLACK_WEBHOOK_COURQE_OTS }} \
            -e GITHUB_REPOSITORY=${{ github.repository }} \
            -e PROJECT_KEY=${{ env.PROJECT_KEY }} \
            -e K6_FILE_NAME=${{ inputs.K6_FILE_NAME }} \
            -e ENVIRONMENT=${{ inputs.ENVIRONMENT }} \
            -e K6_STATSD_NAMESPACE=ots. \
            --env-file ${PWD}/.env/.github_actions \
            $ENV_FILE_OPTION \
            docker.cd.je-labs.com/k6-core:$K6_CORE_VERSION "${{ env.ENTRYPOINT_PATH }}"