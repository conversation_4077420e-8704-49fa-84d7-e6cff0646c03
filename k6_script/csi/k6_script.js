import http from 'k6/http';
import { check, sleep, group, fail } from 'k6';

//########## Add in your k6 script ##########
import {logRequestData} from "../../scripts/components/utils/k6_utils.js";
//########## ---------- ##########

const filepath = './generated_data/k6.log';
import { AWSConfig, SQSClient } from 'https://jslib.k6.io/aws/0.11.0/sqs.js'
const awsConfig = new AWSConfig({
   region: "us-east-1",
   accessKeyId: __ENV.AWS_ACCESS_KEY_ID,
   secretAccessKey: __ENV.AWS_SECRET_ACCESS_KEY,
   sessionToken: __ENV.AWS_SESSION_TOKEN,
})
const sqs = new SQSClient(awsConfig)

export const options = JSON.parse(__ENV.OPTIONS)

let k6_results = [];

function randomNumberInSetRange(min, max) {
    min = Math.ceil(min);
    max = Math.floor(max);
    return Math.floor(Math.random() * (max - min) + min); 
}

function randomNumberOfSetLength(digitsNumber) {
    const min = Math.pow(10, digitsNumber - 1);
    const max = Math.pow(10, digitsNumber) - 1;
    return Math.floor(Math.random() * (max - min + 1)) + min;
}

function randomString(str) {
    var rnd = '';
    while (rnd.length < str) 
        rnd += Math.random().toString(36).substring(2);
    return rnd.substring(0, str);
};

function currentDataUTC(changeValue=0) {
    const dUTC = ( Date.now() - Date.now()%1000 ) / 1000 + changeValue
    return dUTC
};

function currentTimeStamp(changeValue=0) {
    const formDate = new Date();
    formDate.setSeconds(formDate.getSeconds() + changeValue );
    let changeformDate = formDate.toISOString()
    return changeformDate
};

function currentTime(changeValue=0) {
    const oTime = currentTimeStamp(changeValue).split("T")[1].split(".")[0];
    return oTime
};

function currentData(changeValue=0) {
    const oData = currentTimeStamp(changeValue*86400).split("T")[0];
    return oData
};

function currentDayOfWeek(changeValue=0) {
    const days = [
        'Sunday',
        'Monday',
        'Tuesday',
        'Wednesday',
        'Thursday',
        'Friday',
        'Saturday'
      ];
    const curData = new Date();
    const dayOfWeek = (70000 + curData.getDay() + changeValue) % 7;
    return days[dayOfWeek]
};

export default async function () {

    let token
    let totalprice
    let id
    let firstname
    let lastname
    let checkin
    let checkout
    
    let resBody;
    let RequestHeader;
    let RequestBody;
    let ResponseHeader;
    let ResponseBody;
    
    
    group("Ping", function () {
        const req = http.get(`https://restful-booker.herokuapp.com/ping`, 
            JSON.stringify({}),
                {
            headers: {}, timeout: 60000
            }
        );
        const checkOutput = check(req, { 
                'status code MUST be 201': (r) => r.status == 201,
                
            });

        //########## Sending logs to DataDog after call request ##########
        logRequestData(req, checkOutput)
        //########## ---------- ##########
    });

    group("Add token", function () {
        const req = http.post(`https://restful-booker.herokuapp.com/auth`, 
            JSON.stringify(  {  'username': 'admin',  'password': 'password123'  }  ),
                {
            headers:   {  'Content-Type': 'application/json'  }  , timeout: 60000
            }
        );
        const checkOutput = check(req, { 
                'status code MUST be 200': (r) => r.status == 200,
                
            });

        resBody = JSON.parse(req.body);
        token= resBody.token

        //########## Sending logs to DataDog after call request ##########
        logRequestData(req, checkOutput)
        //########## ---------- ##########
    });

    group("Create booking", function () {
        const req = http.post(`https://restful-booker.herokuapp.com/booking`, 
            JSON.stringify( {  'firstname': 'Benjamin',  'lastname' : 'Ivanenko',  'totalprice' : 111,  'depositpaid' : true,  'bookingdates' : {  'checkin' : '2024-06-10',  'checkout' : '2024-06-15'  },  'additionalneeds' : 'Breakfast'  }  ),
                {
            headers:   {  'Accept': 'application/json',  'Content-Type':'application/json'  }  , timeout: 60000
            }
        );
        const checkOutput = check(req, { 
                'status code MUST be 200': (r) => r.status == 200,
                'check body value1': (r) => r.json().booking.totalprice==111, 
                'check body value2': (r) => r.json().booking.firstname=='Benjamin', 
                
            });
        resBody = JSON.parse(req.body);

        //########## Sending logs to DataDog after call request ##########
        logRequestData(req, checkOutput)
        //########## ---------- ##########

        totalprice=resBody.booking.totalprice 
        id=resBody.bookingid 
        firstname=resBody.booking.firstname 
        lastname=resBody.booking.lastname 
        checkin=resBody.booking.bookingdates.checkin 
        checkout=resBody.booking.bookingdates.checkout 
        
        
    });

    group("Update booking", function () {
        const req = http.put(`https://restful-booker.herokuapp.com/booking/${id}`, 
            JSON.stringify( {  'firstname' : 'Stepan',  'lastname' : 'Petrenko',  'totalprice' : 120,  'depositpaid' : true,  'bookingdates' : {    'checkin' : '2024-06-10',    'checkout' : '2024-06-20'  },  'additionalneeds' : 'Breakfast'  }  ),
                {
            headers:   {  'Accept': 'application/json',  'Content-Type': 'application/json',  'Cookie': `token=${token}`  }  , timeout: 60000
            }
        );
        const checkOutput = check(req, { 
                'status code MUST be 200': (r) => r.status == 200,
                'check body value1': (r) => r.json().totalprice==totalprice+9, 
                'check body value2': (r) => r.json().firstname=='Stepan', 
                
            });

        //########## Sending logs to DataDog after call request ##########
        logRequestData(req, checkOutput)
        //########## ---------- ##########
    });

    group("Delete booking", function () {
        const req = http.del(`https://restful-booker.herokuapp.com/booking/${id}`, 
            JSON.stringify({}),
                {
            headers:   {  'Accept': 'application/json',  'Cookie': `token=${token}`  }  , timeout: 60000
            }
        );
        const checkOutput = check(req, { 
                'status code MUST be 201': (r) => r.status == 201,
                
            });

        //########## Sending logs to DataDog after call request ##########
        logRequestData(req, checkOutput)
        //########## ---------- ##########
    });

}