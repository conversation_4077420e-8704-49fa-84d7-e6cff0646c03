import http from 'k6/http';
import { check, group } from "k6";

//########## Add in your k6 script ##########
import {logRequestData} from "../../scripts/components/utils/k6_utils.js";
//########## ---------- ##########

const baseUrl = 'https://restful-booker.herokuapp.com'

export const options = JSON.parse(__ENV.OPTIONS)

export default function () {
    let token
    let bookerId
    let responseBody

    group("Ping", function (){
        let resPing = http.get(`${baseUrl}/ping`)
        const checkResultPing = check(resPing,
            { 'Expected Response Code: 201': (r) => r.status === 201 })

        //########## Sending logs to S3 after call request ##########
        logRequestData(resPing, checkResultPing)
        //########## ---------- ##########
    })

    group("Auth", function (){
        let resAuth = http.post(`${baseUrl}/auth`,
            JSON.stringify({
                "username" : "admin",
                "password" : "password123"
            }),
            {
                headers: {
                    "Content-Type": "application/json"
                }
            })

        const checkResultAuth = check(resAuth,
            { 'Expected Response Code: 200': (r) => r.status === 200 })

        responseBody = JSON.parse(resAuth.body);
        token = responseBody.token;

        //########## Sending logs to DataDog after call request ##########
        logRequestData(resAuth, checkResultAuth)
        //########## ---------- ##########
    })

    group("Create", function (){
        let resCreateBooking = http.post(`${baseUrl}/booking`,
            JSON.stringify({
                "firstname" : "OTS_test",
                "lastname" : "OTS_testov",
                "totalprice" : 111,
                "depositpaid" : true,
                "bookingdates" : {
                    "checkin" : "2024-01-01",
                    "checkout" : "2025-01-01"
                },
                "additionalneeds" : "Breakfast"
            }),
            {
                headers: {
                    "Accept": "application/json",
                    "Content-Type": "application/json"
                }
            })

        const checkResultCreateBooking = check(resCreateBooking,
            { 'Expected Response Code: 200': (r) => r.status === 200 })

        responseBody = JSON.parse(resCreateBooking.body);
        bookerId = responseBody.bookingid;

        //########## Sending logs to DataDog after call request ##########
        logRequestData(resCreateBooking, checkResultCreateBooking)
        //########## ---------- ##########
    })

    group("Update", function (){
        let resUpdateBooking = http.put(`${baseUrl}/booking/${bookerId}`,
            JSON.stringify({
                "firstname" : "OTS_Test_Update",
                "lastname" : "OTS_Testov_Update",
                "totalprice" : 444,
                "depositpaid" : true,
                "bookingdates" : {
                    "checkin" : "2025-01-01",
                    "checkout" : "2025-02-01"
                },
                "additionalneeds" : "Breakfast"
            }),
            {
                headers: {
                    "Accept": "application/json",
                    "Content-Type": "application/json",
                    "Cookie": `token=${token}`
                }
            })

        const checkResultUpdateBooking = check(resUpdateBooking,
            { 'Expected Response Code: 200': (r) => r.status === 200 })

        //########## Sending logs to DataDog after call request ##########
        logRequestData(resUpdateBooking, checkResultUpdateBooking)
        //########## ---------- ##########
    })

    group("Patch booking", function (){
        let resPatchBooking = http.patch(`${baseUrl}/booking/${bookerId}`,
            JSON.stringify({
                "firstname" : "OTS_Test_Patch",
                "lastname" : "OTS_Testov_Patch"
            }),
            {
                headers: {
                    "Accept": "application/json",
                    "Content-Type": "application/json",
                    "Cookie": `token=${token}`
                }
            })

        const checkResultPatchBooking = check(resPatchBooking,
            { 'Expected Response Code: 200': (r) => r.status === 200 })

        //########## Sending logs to DataDog after call request ##########
        logRequestData(resPatchBooking, checkResultPatchBooking)
        //########## ---------- ##########

    })

    group("Delete booking", function (){
        let resDeleteBooking = http.del(`${baseUrl}/booking/${bookerId}`,
            JSON.stringify({}),
            {
                headers: {
                    'Content-Type': 'application/json',
                    'Cookie': `token=${token}`
                }
            })
        const checkResultDeleteBooking = check(resDeleteBooking,
            { 'Expected Response Code: 201': (r) => r.status === 201 })

        //########## Sending logs to DataDog after call request ##########
        logRequestData(resDeleteBooking, checkResultDeleteBooking)
        //########## ---------- ##########
    })
}

