import http from 'k6/http';
import { check, group } from "k6";  // Import group from k6

//########## Add in your k6 script ##########
import { logRequestData } from "../../scripts/components/utils/k6_utils.js";
//########## ---------- ##########

const baseUrl = 'https://reliabilityinsightsapi.pl-soft-change-platass.pdv-2.eu-west-1.qa.jet-internal.com';

export const options = JSON.parse(__ENV.OPTIONS);

export default function search() {

    let data = {
        owners: ["Platforms : ORE : Platform Assurance"],
        status: ["Investigating", "Pending Risk Accept", "In Progress"],
        calc_resolution_date: true
    };

    group("target-rates", function () {
        let url = `${baseUrl}/api/jira/search`;

        let req = http.request('GET', url, JSON.stringify(data), {
            headers: { 'Content-Type': 'application/json' },
        });

        const checkResult = check(req, {
            'status code MUST be 200': (r) => r.status == 200,
        });

        //########## Sending logs to DataDog after call request ##########
        logRequestData(req, checkResult);
        //########## ---------- ##########
    });
}