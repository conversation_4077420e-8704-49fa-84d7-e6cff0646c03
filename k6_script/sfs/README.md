# Running k6 Load Tests with GitHub Actions

This directory contains k6 scripts for load testing the Indirect Taxes service.

## Prerequisites

Install k6 using Homebrew on Macos:

    brew install k6

## Available Tests

- `k6_script/sfs/indirecttaxes_eu1.js`: For the EU1 bulkhead, Staging environment `euw1-pdv-stg-5` (OneEKS)
- `k6_script/sfs/indirecttaxes_stageu1.js`: For the StageEU1 environment (Skip ECS)
- `k6_script/sfs/salestaxes_na1.js`: For the NA1 bulkhead, Staging environment `usw2-pdv-stg-1` (OneEKS)

## Running Tests Locally

1. Navigate to `k6_script/sfs`
2. Modify default options in the script (if needed):
    export const options = JSON.parse(__ENV.OPTIONS || '{"scenarios":{"calculateTaxes":{"executor":"per-vu-iterations","vus":1,"iterations":1,"exec":"calculateTaxes"},"courierCalculateTaxes":{"executor":"per-vu-iterations","vus":1,"iterations":1,"exec":"courierCalculateTaxes"}}}')
    ```
    and run the script
    ```
    k6 run indirecttaxes_eu1.js
    ```
    Or pass options with `-e`:
    ```
    k6 run indirecttaxes_eu1.js -e OPTIONS='{"scenarios":{"calculateTaxes":{"executor":"per-vu-iterations","vus":1,"iterations":1,"exec":"calculateTaxes"},"courierCalculateTaxes":{"executor":"per-vu-iterations","vus":1,"iterations":1,"exec":"courierCalculateTaxes"}}}'
    ```

## Running Tests with GitHub Actions

1. Use an existing k6 script from the `master` branch or create a pull request adding your new or updated script to `ots/k6_script/sfs/`
2. Execute the `run-k6-script` GitHub Action
3. In the workflow, specify:
   - `project_key`: Folder name where script is stored - `sfs`
   - `k6 script name`: Script name to execute - for example `indirecttaxes_eu1.js`
   - Test `options` (see examples below)
5. Run the workflow
6. View test results in [Datadog OTS dashboards](https://app.datadoghq.eu/dashboard/q93-6v3-6av/k6-core?fromUser=true&refresh_mode=sliding&tpl_var_cycle_key%5B0%5D=delivery-r355&view=spans&from_ts=1738952172942&to_ts=1739556972942&live=true)

Your k6 script must include:
`export const options = JSON.parse(__ENV.OPTIONS)`

### Test Options Examples

K6 options examples are provided in the [k6 options Confluence page](https://justeattakeaway.atlassian.net/wiki/spaces/CSI/pages/7396786301/k6+options).

Single test run using `ramping-arrival-rate` executor:
- Ramps up from 100 to 6000 requests per second over 20 minutes
- 30 second warm-up and cool-down periods
- 50-100 preallocated virtual users
```json
{
  "scenarios": {
    "calculateTaxes": {
      "executor": "ramping-arrival-rate",
      "startRate": 0,
      "timeUnit": "1s",
      "preAllocatedVUs": 50,
      "maxVUs": 100,
      "stages": [
        {"target": 100, "duration": "30s"},
        {"target": 6000, "duration": "20m"},
        {"target": 0, "duration": "30s"}
      ],
      "gracefulStop": "30s",
      "exec": "calculateTaxes"
    }
  }
}
```

Two parallel tests run using ramping-arrival-rate executor:
- Each test ramps up from 100 to 4000 requests per second over 20 minutes
- 30 second warm-up and cool-down periods
- 50-100 preallocated virtual users per test
```json
{
  "scenarios": {
    "calculateTaxes": {
      "executor": "ramping-arrival-rate",
      "startRate": 0,
      "timeUnit": "1s",
      "preAllocatedVUs": 50,
      "maxVUs": 100,
      "stages": [
        {"target": 100, "duration": "30s"},
        {"target": 4000, "duration": "20m"},
        {"target": 0, "duration": "30s"}
      ],
      "gracefulStop": "30s",
      "exec": "calculateTaxes"
    },
    "courierCalculateTaxes": {
      "executor": "ramping-arrival-rate",
      "startRate": 0,
      "timeUnit": "1s",
      "preAllocatedVUs": 50,
      "maxVUs": 100,
      "stages": [
        {"target": 100, "duration": "30s"},
        {"target": 4000, "duration": "20m"},
        {"target": 0, "duration": "30s"}
      ],
      "gracefulStop": "30s",
      "exec": "courierCalculateTaxes"
    }
  }
}
```

> **Note**: Before pasting the JSON into the options input in the GitHub Action, compact it to a single line and remove all whitespace.

```markdown
See [k6 options](https://justeattakeaway.atlassian.net/wiki/spaces/CSI/pages/7396786301/k6+options) for more examples and check the [official documentation](https://grafana.com/docs/k6/latest/using-k6/k6-options/) for more information.
```
