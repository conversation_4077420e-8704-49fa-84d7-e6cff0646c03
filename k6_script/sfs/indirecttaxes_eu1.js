import http from 'k6/http';
import { check } from "k6";

let file;
try {
    file = require('k6/x/file');
} catch (error) {
    file = {
        appendString: (path, data) => console.log(`[MOCK] Writing to ${path}: ${data}`)
    };
}

//########## Add in your k6 script ##########
import {logRequestData} from "../../scripts/components/utils/k6_utils.js";
//########## ---------- ##########

// Get base URL from environment variable
const baseUrl = __ENV.INDIRECT_TAXES_TAX_ENGINE || 'https://indirecttaxes.pa-partner-indtax.pdv-5.eu-west-1.staging.jet-internal.com';

export const options = JSON.parse(__ENV.OPTIONS || '{"scenarios":{"calculateTaxes":{"executor":"per-vu-iterations","vus":1,"iterations":1,"exec":"calculateTaxes"},"courierCalculateTaxes":{"executor":"per-vu-iterations","vus":1,"iterations":1,"exec":"courierCalculateTaxes"}}}')

export function calculateTaxes() {
    const url = `${baseUrl}/taxes/nl/calculateTaxes`;
    const timestampMs = new Date().getTime();

    const payload = {
        locationId: "LOC_OF_0015",
        "applicableDate": timestampMs,
        revenueCategory: [
            { name: "SERVICE_FEE", centsPrice: 10000 },
            { name: "TIP", centsPrice: 5000 },
            { name: "HOLD_FEE", centsPrice: 5000 }
        ]
    };

    const params = {
        headers: {
            'Content-Type': 'application/json',
        },
    };

    const response = http.post(url, JSON.stringify(payload), params);

    // Basic response validation
    const checkResult = check(response, {
        'status code MUST be 200': (r) => r.status === 200,
        'response must be JSON': (r) => r.headers['Content-Type'] && r.headers['Content-Type'].includes('application/json'),
        'response must have calculatedTaxes array': (r) => r.json().hasOwnProperty('calculatedTaxes'),
        'calculatedTaxes must be non-empty array': (r) => Array.isArray(r.json().calculatedTaxes) && r.json().calculatedTaxes.length > 0,
        'each calculatedTaxes item must have revenueCategory': (r) => r.json().calculatedTaxes.every(item => item.hasOwnProperty('revenueCategory')),
        'each calculatedTaxes item must have taxes array': (r) => r.json().calculatedTaxes.every(item => Array.isArray(item.taxes))
    });

    //########## Sending logs to S3 after call request ##########
    logRequestData(response, checkResult);
    //########## ---------- ##########
}

export function courierCalculateTaxes() {
    const url = `${baseUrl}/taxes/sk/courier/calculateTaxes`;
    const timestampMs = new Date().getTime();

    const payload = {
        "locationId": "LOC_OF_0091",
        "applicableDate": timestampMs,
        "spendCategory": [
            {
                "name": "COURIER_TOTAL",
                "vatRegistered": true,
                "centsPrice": 100
            },
            {
                "name": "COURIER_TOTAL",
                "vatRegistered": false,
                "centsPrice": 200
            }
        ]
    };

    const params = {
        headers: {
            'Content-Type': 'application/json',
        },
    };

    const response = http.post(url, JSON.stringify(payload), params);

    // Basic response validation
    const checkResult = check(response, {
        'status code MUST be 200': (r) => r.status === 200,
        'response must be JSON': (r) => r.headers['Content-Type'] && r.headers['Content-Type'].includes('application/json'),
        'response must have calculatedTaxes array': (r) => r.json().hasOwnProperty('calculatedTaxes'),
        'calculatedTaxes must be non-empty array': (r) => Array.isArray(r.json().calculatedTaxes) && r.json().calculatedTaxes.length > 0,
        'each calculatedTaxes item must have spendCategory': (r) => r.json().calculatedTaxes.every(item => item.hasOwnProperty('spendCategory')),
        'each calculatedTaxes item must have taxes array': (r) => r.json().calculatedTaxes.every(item => Array.isArray(item.taxes))
    });

    //########## Sending logs to S3 after call request ##########
    logRequestData(response, checkResult);
    //########## ---------- ##########
}

// Add a default function to handle cases where no specific scenario is provided
export default function () {
    calculateTaxes();
}