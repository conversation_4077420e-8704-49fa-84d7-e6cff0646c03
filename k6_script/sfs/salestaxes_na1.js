import http from 'k6/http';
import { check } from "k6";

let file;
try {
    file = require('k6/x/file');
} catch (error) {
    file = {
        appendString: (path, data) => console.log(`[MOCK] Writing to ${path}: ${data}`)
    };
}

//########## Add in your k6 script ##########
import {logRequestData} from "../../scripts/components/utils/k6_utils.js";
//########## ---------- ##########

// Get base URL from environment variable
const baseUrl = __ENV.SALES_TAXES_SERVICE_ENGINE || 'https://salestaxes-ca.cu-service-engine.staging.jet-internal.com';

const defaultOptions = {
  "scenarios": {
    "calculateMembershipTaxes": {
      "executor": "ramping-arrival-rate",
      "startRate": 0,
      "timeUnit": "1s",
      "preAllocatedVUs": 50,
      "maxVUs": 100,
      "stages": [
        {"target": 100, "duration": "30s"},
        {"target": 2000, "duration": "20m"},
        {"target": 0, "duration": "30s"}
      ],
      "gracefulStop": "30s",
      "exec": "calculateMembershipTaxes"
    }
  },
  "thresholds": {
    "http_req_duration": ["p(95)<500"], // 95% of requests must complete below 500ms
    "http_req_failed": ["rate<0.01"],   // Less than 1% of requests can fail
    "http_reqs": ["rate>500"]          // Throughput should be at least 500 RPS
  }
};

export const options = __ENV.OPTIONS ? JSON.parse(__ENV.OPTIONS) : defaultOptions;

export function calculateMembershipTaxes() {
    const url = `${baseUrl}/v1/taxes/CA/calculateTaxes/membership`;

    // Define tax zones and prices for parameterization
    const taxZones = ['MB', 'ON', 'BC', 'AB'];
    const prices = [1000, 2500, 5000, 10000];

    // Randomly select tax zone and price
    const selectedZone = taxZones[Math.floor(Math.random() * taxZones.length)];
    const selectedPrice = prices[Math.floor(Math.random() * prices.length)];

    const currentTimestamp = new Date().getTime();

    const payload = {
        taxZone: selectedZone,
        applicableDate: currentTimestamp,
        taxCategories: [
            {
                taxCategory: "MEMBERSHIP_FEE",
                centsPrice: selectedPrice
            }
        ]
    };

    const params = {
        headers: {
            'Content-Type': 'application/json',
        },
    };

    const response = http.post(url, JSON.stringify(payload), params);

    // Common validation checks (without GST check)
    const commonChecks = {
        'status code MUST be 200': (r) => r.status === 200,
        'response must be JSON': (r) => r.headers['Content-Type'] && r.headers['Content-Type'].includes('application/json'),
        'response must have taxCategoryResponses array': (r) => r.json().hasOwnProperty('taxCategoryResponses'),
        'taxCategoryResponses must be non-empty array': (r) => Array.isArray(r.json().taxCategoryResponses) && r.json().taxCategoryResponses.length > 0,
        'each taxCategoryResponse must have taxCategory': (r) => r.json().taxCategoryResponses.every(item => item.hasOwnProperty('taxCategory')),
        'each taxCategoryResponse must have taxes object': (r) => r.json().taxCategoryResponses.every(item => item.hasOwnProperty('taxes')),
        'each taxCategoryResponse must have rates object': (r) => r.json().taxCategoryResponses.every(item => item.hasOwnProperty('rates'))
    };

    // Zone-specific checks
    let zoneSpecificChecks = {};

    switch(selectedZone) {
        case 'MB':
            zoneSpecificChecks = {
                'GST tax must be present': (r) => r.json().taxCategoryResponses[0].taxes.hasOwnProperty('GST'),
                'GST_RATE must be present': (r) => r.json().taxCategoryResponses[0].rates.hasOwnProperty('GST_RATE'),
                'MB_PST tax must be present': (r) => r.json().taxCategoryResponses[0].taxes.hasOwnProperty('MB_PST'),
                'MB_PST_RATE must be present': (r) => r.json().taxCategoryResponses[0].rates.hasOwnProperty('MB_PST_RATE')
            };
            break;
        case 'ON':
            zoneSpecificChecks = {
                'ON_HST tax must be present': (r) => r.json().taxCategoryResponses[0].taxes.hasOwnProperty('ON_HST'),
                'ON_HST_RATE must be present': (r) => r.json().taxCategoryResponses[0].rates.hasOwnProperty('ON_HST_RATE')
            };
            break;
        case 'BC':
            zoneSpecificChecks = {
                'GST tax must be present': (r) => r.json().taxCategoryResponses[0].taxes.hasOwnProperty('GST'),
                'GST_RATE must be present': (r) => r.json().taxCategoryResponses[0].rates.hasOwnProperty('GST_RATE'),
                'BC_PST tax must be present': (r) => r.json().taxCategoryResponses[0].taxes.hasOwnProperty('BC_PST'),
                'BC_PST_RATE must be present': (r) => r.json().taxCategoryResponses[0].rates.hasOwnProperty('BC_PST_RATE')
            };
            break;
        case 'AB':
            zoneSpecificChecks = {
                'GST tax must be present': (r) => r.json().taxCategoryResponses[0].taxes.hasOwnProperty('GST'),
                'GST_RATE must be present': (r) => r.json().taxCategoryResponses[0].rates.hasOwnProperty('GST_RATE')
            };
            break;
    }

    // Combine common and zone-specific checks
    const allChecks = {...commonChecks, ...zoneSpecificChecks};
    const checkResult = check(response, allChecks);

    //########## Sending logs to S3 after call request ##########
    logRequestData(response, checkResult);
    //########## ---------- ##########
}

// Add a default function to handle cases where no specific scenario is provided
export default function () {
    calculateMembershipTaxes();
}